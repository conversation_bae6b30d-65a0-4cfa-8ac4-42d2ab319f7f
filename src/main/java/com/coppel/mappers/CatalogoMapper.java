package com.coppel.mappers;

import java.lang.reflect.Method;

import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import com.coppel.dto.CatalogoDTO;

public class CatalogoMapper {
    private CatalogoMapper() {
        throw new IllegalStateException("No existe un constructor para la clase");
    }

    public static CatalogoDTO mapCatalogoDTO(Object cat) {
        Object[] values = (Object[]) cat;
        CatalogoDTO dto = new CatalogoDTO();
        Method[] methods = CatalogoDTO.class.getMethods();

        try {
            for (int i = 0; i < values.length; i++) {
                Method setter = findSetter(methods, i);
                if (setter != null) {
                    setter.invoke(dto, values[i]);
                }
            }
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al mapear catalogo", e);
        }
        return dto;
    }

    private static Method findSetter(Method[] methods, int index) {
        switch (index) {
            case 0:
                return getMethodByName(methods, "setId");
            case 1:
                return getMethodByName(methods, "setCatalogo");
            case 2:
                return getMethodByName(methods, "setFecha");
            case 3:
                return getMethodByName(methods, "setIsActive");
            default:
                return null;
        }
    }

    private static Method getMethodByName(Method[] methods, String name) {
        for (Method method : methods) {
            if (method.getName().equals(name)) {
                return method;
            }
        }
        return null;
    }

}
