package com.coppel.services;

import java.util.List;

import com.coppel.dto.CatEmpDTO;
import com.coppel.dto.CatLQsQDTO;
import com.coppel.dto.CatSHCPDTO;
import com.coppel.dto.CatalogoDTO;

public interface CatalogoService {

    public Boolean limpiarCatQsQ();

    public Number actualizaCatalogoQeQ();

    public Number obtenerTotalRegistros(String tablename);

    public Boolean guardarArchivoQsQ(List<CatLQsQDTO> lista);

    public Boolean respaldoSHCP();

    public Boolean guardarArchivoSHCP(List<CatSHCPDTO> lista);

    public List<CatalogoDTO> obtenerCats();

    public CatalogoDTO actualizarFechaCat(Long idCat);

    public Boolean guardarArchivoEMP(List<CatEmpDTO> lista);

    public Boolean respaldoEmp();
}
