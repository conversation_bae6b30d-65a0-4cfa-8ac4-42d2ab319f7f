package com.coppel.services.impl;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.Proxy.Type;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ResponseStatusException;

import com.coppel.dto.BanxicoResponseDTO;
import com.coppel.repositories.TipoCambioRepository;
import com.coppel.services.TipoCambioService;

@Service
public class TipoCambioServiceImpl implements TipoCambioService {

    @Autowired
    private TipoCambioRepository tipoCambioRepository;

    @Value("${proxy.host}")
    private String host;
    @Value("${proxy.port}")
    private int port;
    @Value("${clients.uriBanxico}")
    private String urlBanxico;
    @Value("${clients.bmxToken}")
    private String bmxToken;
    @Value("${proxy.isRequired}")
    private boolean isRequired;

    SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
    RestTemplate restTemplate;
    Proxy proxy;

    @Override
    public BanxicoResponseDTO guardarTipoCambio(BanxicoResponseDTO tipoCambio) {
        tipoCambioRepository.guardarTipoCambio(tipoCambio);
        return tipoCambio;
    }

    @Override
    public String obtenerTipoDeCambio() {
        if (isRequired) {
            proxy = new Proxy(Type.HTTP, new InetSocketAddress(host, port));
        }
        requestFactory.setProxy(proxy);
        restTemplate = new RestTemplate(requestFactory);

        // Obtener la fecha actual
        LocalDate currentDate = LocalDate.now();
    
        // Definir el formato deseado
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        // Formatear la fecha actual
        String dateFormattered = currentDate.format(formatter);
        String dateRange = dateFormattered + "/" + dateFormattered;
        String url = urlBanxico + dateRange;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Bmx-Token", bmxToken);

        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<BanxicoResponseDTO> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                request,
                BanxicoResponseDTO.class);

        if (response.getStatusCode() == HttpStatus.OK) {
            return this.guardarTipoCambio(response.getBody()).getBmx().getSeries().get(0).getDatos().get(0).getDato();
        } else {
           throw new ResponseStatusException(HttpStatus.CONFLICT, "Error al obtener tipo de cambio desde banxico" + response.getStatusCode());
        }
    }

}
