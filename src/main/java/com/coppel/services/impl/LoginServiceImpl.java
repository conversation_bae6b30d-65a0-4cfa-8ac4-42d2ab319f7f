package com.coppel.services.impl;

import java.util.Base64;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.coppel.repositories.LoginRepository;
import com.coppel.services.LoginService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class LoginServiceImpl implements LoginService {

    @Autowired
    LoginRepository loginRepository;

    @Override
    public Boolean isUserValid(String employeeData) {
        // Divide la cadena en partes utilizando "centro=" como delimitador
        String[] parts = employeeData.split("centro=");
        String centerPart = parts[1].split(",")[0].trim();

        // Elimina cualquier carácter no numérico al final de la cadena
        centerPart = centerPart.replaceAll("[^\\d]", "");

        // Convierte el valor de "centro" a long
        Long centerId = Long.parseLong(centerPart);

        return loginRepository.isUserValid(centerId);
    }

    @Override
    public Long deserializeJwt(String tokenazuread) throws JsonProcessingException {
        if (tokenazuread == null || tokenazuread.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "El token JWT proporcionado es nulo o vacío");
        }

        try {
            String[] head = tokenazuread.split("\\.");
            Base64.Decoder decoder = Base64.getUrlDecoder();
            Object header = new String(decoder.decode(head[1]));
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(String.valueOf(header));

            return jsonNode.get("employeeId").asLong();
            // Asegúrate de verificar la validez del token antes de continuar
        } catch (JsonProcessingException e) {
            // Manejo de la excepción específica de deserialización
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Error al deserializar el token JWT", e);
        } catch (Exception e) {
            // Manejo de otras excepciones si es necesario
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Error inesperado al procesar el token JWT", e);
        }
    }

}
