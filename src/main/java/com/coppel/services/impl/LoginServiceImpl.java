package com.coppel.services.impl;

import java.util.Base64;
import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.coppel.repositories.LoginRepository;
import com.coppel.services.LoginService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class LoginServiceImpl implements LoginService {

    private static final Logger logger = LoggerFactory.getLogger(LoginServiceImpl.class); // SLF4J

    @Autowired
    LoginRepository loginRepository;

    @Override
    public Boolean isUserValid(String employeeData) {
        LocalDateTime fechaActual = LocalDateTime.now();
        String dataLength = employeeData != null ? String.valueOf(employeeData.length()) : "null";

        // 1. System.out.println - Para Kubernetes stdout
        System.out.println("[STDOUT] [LOGIN-SERVICE] Iniciando validacion de usuario - time: " + fechaActual + " - data_length: " + dataLength);

        // 2. SLF4J Logger
        logger.info("[SLF4J] [LOGIN-SERVICE] Iniciando validacion de usuario - data_length: {}", dataLength);

        try {
            // Divide la cadena en partes utilizando "centro=" como delimitador
            String[] parts = employeeData.split("centro=");

            System.out.println("[STDOUT] [LOGIN-SERVICE] Partes divididas - total: " + parts.length);
            logger.info("[SLF4J] [LOGIN-SERVICE] Partes divididas - total: {}", parts.length);

            String centerPart = parts[1].split(",")[0].trim();

            System.out.println("[STDOUT] [LOGIN-SERVICE] Centro extraido raw: " + centerPart);
            logger.info("[SLF4J] [LOGIN-SERVICE] Centro extraido raw: {}", centerPart);

            // Elimina cualquier carácter no numérico al final de la cadena
            centerPart = centerPart.replaceAll("[^\\d]", "");

            System.out.println("[STDOUT] [LOGIN-SERVICE] Centro limpio: " + centerPart);
            logger.info("[SLF4J] [LOGIN-SERVICE] Centro limpio: {}", centerPart);

            // Convierte el valor de "centro" a long
            Long centerId = Long.parseLong(centerPart);

            System.out.println("[STDOUT] [LOGIN-SERVICE] Centro ID: " + centerId);
            logger.info("[SLF4J] [LOGIN-SERVICE] Centro ID: {}", centerId);

            Boolean isValid = loginRepository.isUserValid(centerId);

            System.out.println("[STDOUT] [LOGIN-SERVICE] Validacion completada: " + isValid);
            logger.info("[SLF4J] [LOGIN-SERVICE] Validacion completada: {}", isValid);

            return isValid;

        } catch (Exception e) {
            System.out.println("[STDOUT] [LOGIN-SERVICE] ERROR en validacion: " + e.getMessage());
            logger.error("[SLF4J] [LOGIN-SERVICE] ERROR en validacion: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long deserializeJwt(String tokenazuread) throws JsonProcessingException {
        if (tokenazuread == null || tokenazuread.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "El token JWT proporcionado es nulo o vacío");
        }

        try {
            String[] head = tokenazuread.split("\\.");
            Base64.Decoder decoder = Base64.getUrlDecoder();
            Object header = new String(decoder.decode(head[1]));
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(String.valueOf(header));

            return jsonNode.get("employeeId").asLong();
            // Asegúrate de verificar la validez del token antes de continuar
        } catch (JsonProcessingException e) {
            // Manejo de la excepción específica de deserialización
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Error al deserializar el token JWT", e);
        } catch (Exception e) {
            // Manejo de otras excepciones si es necesario
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Error inesperado al procesar el token JWT", e);
        }
    }

}
