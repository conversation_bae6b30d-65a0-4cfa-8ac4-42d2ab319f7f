package com.coppel.services.impl;

import java.util.Base64;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.coppel.repositories.LoginRepository;
import com.coppel.services.LoginService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class LoginServiceImpl implements LoginService {

    private static final Logger logger = LoggerFactory.getLogger(LoginServiceImpl.class);

    @Autowired
    LoginRepository loginRepository;

    @Override
    public Boolean isUserValid(String employeeData) {
        logger.info("[LOGIN-SERVICE] Iniciando validación de usuario");
        logger.info("[LOGIN-SERVICE] Datos del empleado: {}", employeeData);

        try {
            // Divide la cadena en partes utilizando "centro=" como delimitador
            String[] parts = employeeData.split("centro=");
            logger.info("[LOGIN-SERVICE] Partes divididas: {}", java.util.Arrays.toString(parts));

            String centerPart = parts[1].split(",")[0].trim();
            logger.info("[LOGIN-SERVICE] Centro extraído (raw): {}", centerPart);

            // Elimina cualquier carácter no numérico al final de la cadena
            centerPart = centerPart.replaceAll("[^\\d]", "");
            logger.info("[LOGIN-SERVICE] Centro limpio: {}", centerPart);

            // Convierte el valor de "centro" a long
            Long centerId = Long.parseLong(centerPart);
            logger.info("[LOGIN-SERVICE] Centro ID: {}", centerId);

            Boolean isValid = loginRepository.isUserValid(centerId);
            logger.info("[LOGIN-SERVICE] Validación completada: {}", isValid);
            return isValid;

        } catch (Exception e) {
            logger.error("[LOGIN-SERVICE] Error en validación: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long deserializeJwt(String tokenazuread) throws JsonProcessingException {
        if (tokenazuread == null || tokenazuread.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "El token JWT proporcionado es nulo o vacío");
        }

        try {
            String[] head = tokenazuread.split("\\.");
            Base64.Decoder decoder = Base64.getUrlDecoder();
            Object header = new String(decoder.decode(head[1]));
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(String.valueOf(header));

            return jsonNode.get("employeeId").asLong();
            // Asegúrate de verificar la validez del token antes de continuar
        } catch (JsonProcessingException e) {
            // Manejo de la excepción específica de deserialización
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Error al deserializar el token JWT", e);
        } catch (Exception e) {
            // Manejo de otras excepciones si es necesario
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Error inesperado al procesar el token JWT", e);
        }
    }

}
