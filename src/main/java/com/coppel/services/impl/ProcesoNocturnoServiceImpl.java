package com.coppel.services.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.coppel.dto.ProcesoNocturnoDTO;
import com.coppel.repositories.ProcesoNocturnoRepository;
import com.coppel.services.ProcesoNocturnoService;

@Service
public class ProcesoNocturnoServiceImpl implements ProcesoNocturnoService {
  @Autowired
  ProcesoNocturnoRepository procesoNocturnoRepositoy;

  @Override
  public List<Object[]> ejecutarFuncionPld(ProcesoNocturnoDTO proceso) {
    return procesoNocturnoRepositoy.ejecutarFuncionPld(proceso);
  }

}
