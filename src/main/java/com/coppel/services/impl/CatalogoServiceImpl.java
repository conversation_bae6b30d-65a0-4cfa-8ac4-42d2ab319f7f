package com.coppel.services.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.coppel.dto.CatEmpDTO;
import com.coppel.dto.CatLQsQDTO;
import com.coppel.dto.CatSHCPDTO;
import com.coppel.dto.CatalogoDTO;
import com.coppel.repositories.CatalogoRepository;
import com.coppel.services.CatalogoService;

@Service
public class CatalogoServiceImpl implements CatalogoService {

    @Autowired
    CatalogoRepository catalogoRepository;

    @Override
    public Number actualizaCatalogoQeQ() {
        // Actualizar Lista Negra Quien es Quien Respaldo
        if (!Boolean.TRUE.equals(catalogoRepository.actualizaLNQsQRespaldo())) {
            return 0;
        }

        // Obtener los datos de cat_listadoquienesquien e insertarlo
        // ListaNegraQuienEsQuien_copia
        if (!Boolean.TRUE.equals(catalogoRepository.pasarCatLQsQHaciaLNQsQCopia())) {
            return 0;
        }

        // Comprobar que la inserción fue correcta
        if (!catalogoRepository.areCountsEqual()) {
            return 0;
        }

        // Actualizar Lista Negra Quien Es Quien
        catalogoRepository.eliminarLNQsQ();

        // Actualizar ListaNegraQuienEsQuien
        catalogoRepository.guardarLNQsQ();

        // Crear nuevo cat
        if (!Boolean.TRUE.equals(catalogoRepository.crearNuevaCatLNQsQ())) {
            return 0;
        }

        // Guardar paises bloqueados
        if (!Boolean.TRUE.equals(catalogoRepository.guardarPaisesBloqueadosQsQ())) {
            return 0;
        }

        return catalogoRepository.obtenerRegistrosPorCatalogo("cat_listadoquienesquien");
    }

    @Override
    public Boolean guardarArchivoQsQ(List<CatLQsQDTO> lista) {
        for (CatLQsQDTO catLQ : lista) {
            catalogoRepository.guardarArchivoQsQ(catLQ);
        }
        return true;
    }

    @Override
    public Boolean limpiarCatQsQ() {
        catalogoRepository.limpiarCatQsQ();
        return true;
    }

    @Override
    public Boolean respaldoSHCP() {
        return catalogoRepository.respaldoSHCP();
    }

    @Override
    public Boolean guardarArchivoSHCP(List<CatSHCPDTO> lista) {
        for (CatSHCPDTO cat : lista) {
            catalogoRepository.guardarArchivoSHCP(cat);
        }
        return true;
    }

    @Override
    public List<CatalogoDTO> obtenerCats() {
        return catalogoRepository.obtenerCats();
    }

    @Override
    public CatalogoDTO actualizarFechaCat(Long idCat) {

        if (idCat == 2) {
            catalogoRepository.respaldoCatListadoSHCP();
        }

        return catalogoRepository.actualizarFechaCat(idCat);
    }

    @Override
    public Boolean guardarArchivoEMP(List<CatEmpDTO> lista) {
        for (CatEmpDTO cat : lista) {
            catalogoRepository.guardarArchivoEMP(cat);
        }
        return true;
    }

    @Override
    public Boolean respaldoEmp() {
        return catalogoRepository.respaldoEmp();
    }

    @Override
    public Number obtenerTotalRegistros(String tablename) {
        return catalogoRepository.obtenerRegistrosPorCatalogo(tablename);
    }

}
