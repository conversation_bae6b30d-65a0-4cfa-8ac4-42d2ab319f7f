package com.coppel.execeptions;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.coppel.dto.ApiResponseDTO;
import com.coppel.util.AppMessages;
import com.coppel.util.Meta;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * Clase para manejo de excepciones no controladas.
 */
@ControllerAdvice
public class AppExceptionHandler extends ResponseEntityExceptionHandler {

        HttpServletResponse response;
        HttpServletRequest request;
        // Usuario
        @Value("${spring.datasource.username}")
        private String username;

        private Logger log = Logger.getLogger(AppExceptionHandler.class.getName());

        @ExceptionHandler(value = { ResponseStatusException.class })
        public ResponseEntity<Object> handleResponseStatusException(ResponseStatusException ex,
                        HttpServletRequest request)
                        throws UnknownHostException {
                LocalDateTime fechaActual = LocalDateTime.now();

                // Logs adicionales para rastrear errores que causan content vacío
                System.out.println("[STDOUT] [EXCEPTION-HANDLER] ResponseStatusException capturada - time: " + fechaActual);
                System.out.println("[STDOUT] [EXCEPTION-HANDLER] Status code: " + ex.getStatusCode().value());
                System.out.println("[STDOUT] [EXCEPTION-HANDLER] Reason: " + ex.getReason());
                System.out.println("[STDOUT] [EXCEPTION-HANDLER] URL: " + request.getRequestURL());
                System.out.println("[STDOUT] [EXCEPTION-HANDLER] Method: " + request.getMethod());

                Meta meta = new Meta();
                meta.setDevMessage(null);
                meta.setStatus(AppMessages.CLIENT_ERROR);
                meta.setStatusCode(ex.getStatusCode().value());
                meta.setMessage(ex.getReason());
                meta.setTimestamp(fechaActual.toString());
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

                // IMPORTANTE: Aquí se crea ApiResponseDTO con Optional.empty() - esto causa content vacío
                System.out.println("[STDOUT] [EXCEPTION-HANDLER] ATENCION: Creando ApiResponseDTO con Optional.empty() - el parametrizable recibira data vacio");
                ApiResponseDTO apiResponse = new ApiResponseDTO(meta, Optional.empty());

                // Obtener ip
                String ipAddress = this.getLocalhostIp();

                // Log para errores
                String logs = String.format("[time: %s] [ip: %s] [user: %s] -- [ %s %s ]",
                                fechaActual, ipAddress, username, request.getMethod(),
                                500);
                log.log(Level.WARNING, logs, ex);

                return new ResponseEntity<>(apiResponse, httpHeaders, ex.getStatusCode());
        }

        /**
         * Cualquier excepción que no sea atendida será tratada en en este método.
         * 
         * @param runtimeException
         * @param webRequest
         * @return ResponseEntity<Object>
         * @throws UnknownHostException
         */
        @ExceptionHandler(value = { Exception.class })
        protected ResponseEntity<Object> handleException(RuntimeException runtimeException, HttpServletRequest request,
                        WebRequest webRequest)
                        throws UnknownHostException {
                Meta meta = new Meta();
                meta.setStatus(AppMessages.ERROR);
                meta.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
                meta.setDevMessage(runtimeException.getMessage() == null ? runtimeException.getClass().toString()
                                : runtimeException.getMessage());
                meta.setTimestamp(LocalDateTime.now().toString());
                meta.setTransactionID(null);
                ApiResponseDTO apiResponse = new ApiResponseDTO(meta, Optional.empty());
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

                // Obtener ip
                String ipAddress = this.getLocalhostIp();

                // Log para errores
                LocalDateTime fechaActual = LocalDateTime.now();
                String logs = String.format("[time: %s] [ip: %s] [user: %s]  -- [ %s %s ]",
                                fechaActual, ipAddress, username, request.getMethod(),
                                500);
                log.log(Level.WARNING, logs, runtimeException);

                return handleExceptionInternal(
                                runtimeException,
                                apiResponse,
                                httpHeaders,
                                HttpStatus.INTERNAL_SERVER_ERROR,
                                webRequest);
        }

        public String getLocalhostIp() throws UnknownHostException {
                return InetAddress.getLocalHost().getHostAddress();
        }

}
