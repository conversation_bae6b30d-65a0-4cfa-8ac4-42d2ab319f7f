package com.coppel.controllers;

import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.coppel.dto.ApiResponseDTO;
import com.coppel.dto.CatEmpDTO;
import com.coppel.dto.CatLQsQDTO;
import com.coppel.dto.CatSHCPDTO;
import com.coppel.services.CatalogoService;
import com.coppel.util.Meta;
import org.springframework.web.bind.annotation.GetMapping;

@RestController
public class CatalogosController {

    private static final Logger logger = LoggerFactory.getLogger(CatalogosController.class);
    private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);

    @Autowired
    CatalogoService catalogoService;

    // Todos los catalogos
    @GetMapping("/obtenerCats")
    public ApiResponseDTO obtenerCats() {
        logger.info("[CATALOGOS] Iniciando obtención de todos los catálogos");
        try {
            var catalogos = catalogoService.obtenerCats();
            logger.info("[CATALOGOS] Catálogos obtenidos exitosamente. Total: {}", catalogos.size());
            return new ApiResponseDTO(meta, catalogos);
        } catch (Exception e) {
            logger.error("[CATALOGOS] Error al obtener catálogos: {}", e.getMessage(), e);
            throw e;
        }
    }

    @PutMapping("/actualizarFechaCat")
    public ApiResponseDTO actualizarFechaCat(@RequestBody Long idCat) {
        return new ApiResponseDTO(meta, catalogoService.actualizarFechaCat(idCat));
    }

    @PostMapping(value = "/obtenerTotalRegistros")
    public ApiResponseDTO obtenerTotalRegistros(@RequestBody String tablename) {
        return new ApiResponseDTO(meta, catalogoService.obtenerTotalRegistros(tablename));
    }

    // Métodos Catalogo Quien es Quien
    @PostMapping(value = "/limpiarCatQsQ")
    public ApiResponseDTO limpiarCatQsQ() {
        return new ApiResponseDTO(meta, catalogoService.limpiarCatQsQ());
    }

    @PostMapping(value = "/guardarArchivoQsQ")
    public ApiResponseDTO guardarArchivoQsQ(@RequestBody List<CatLQsQDTO> lista) {
        logger.info("[CATALOGOS-QsQ] Iniciando guardado de archivo Quién es Quién");
        logger.info("[CATALOGOS-QsQ] Registros recibidos: {}", lista != null ? lista.size() : 0);
        try {
            Boolean resultado = catalogoService.guardarArchivoQsQ(lista);
            logger.info("[CATALOGOS-QsQ] Archivo guardado exitosamente: {}", resultado);
            return new ApiResponseDTO(meta, resultado);
        } catch (Exception e) {
            logger.error("[CATALOGOS-QsQ] Error al guardar archivo: {}", e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping(value = "/actualizaCatalogoQeQ")
    public ApiResponseDTO actualizaCatalogoQeQ() {
        return new ApiResponseDTO(meta, catalogoService.actualizaCatalogoQeQ());
    }

    // Métodos Catalogo SHCP
    @PostMapping(value = "/respaldoSHCP")
    public ApiResponseDTO respaldoSHCP() {
        return new ApiResponseDTO(meta, catalogoService.respaldoSHCP());
    }

    @PostMapping(value = "/guardarArchivoSHCP")
    public ApiResponseDTO guardarArchivoSHCP(@RequestBody List<CatSHCPDTO> lista) {
        return new ApiResponseDTO(meta, catalogoService.guardarArchivoSHCP(lista));
    }

    // Métodos Catalogo EMPLEADOS
    @PostMapping(value = "/guardarArchivoEmp")
    public ApiResponseDTO guardarArchivoEMP(@RequestBody List<CatEmpDTO> lista) {
        return new ApiResponseDTO(meta, catalogoService.guardarArchivoEMP(lista));
    }

    @PostMapping(value = "/respaldoEmp")
    public ApiResponseDTO respaldoEmp() {
        return new ApiResponseDTO(meta, catalogoService.respaldoEmp());
    }

}
