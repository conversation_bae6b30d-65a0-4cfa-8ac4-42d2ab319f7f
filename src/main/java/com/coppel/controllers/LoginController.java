package com.coppel.controllers;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.coppel.clients.PersonalClient;
import com.coppel.dto.ApiResponseDTO;
import com.coppel.services.LoginService;
import com.coppel.util.Meta;
import com.fasterxml.jackson.core.JsonProcessingException;

@RestController
public class LoginController {

    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);
    private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);

    @Autowired
    PersonalClient personalClient;
    @Autowired
    LoginService loginService;

    @PostMapping("/isUserValid")
    public ApiResponseDTO isUserValid(@RequestBody String tokenazuread) throws JsonProcessingException {
        System.out.println("🔐 [LOGIN] Iniciando validación de usuario");
        System.out.println("🔐 [LOGIN] Token recibido: " + (tokenazuread != null ? "Token presente (longitud: " + tokenazuread.length() + ")" : "Token nulo"));

        try {
            System.out.println("🔐 [LOGIN] Deserializando JWT...");
            Long employeeId = loginService.deserializeJwt(tokenazuread);
            System.out.println("🔐 [LOGIN] Employee ID extraído del JWT: " + employeeId);

            System.out.println("🔐 [LOGIN] Consultando información del empleado en PersonalClient...");
            String employeeData = personalClient.obtenerEmpleadoInfo(employeeId).getData().toString();
            System.out.println("🔐 [LOGIN] Datos del empleado obtenidos: " + employeeData);

            System.out.println("🔐 [LOGIN] Validando usuario en base de datos...");
            Boolean isValid = loginService.isUserValid(employeeData);
            System.out.println("🔐 [LOGIN] Resultado de validación: " + isValid);

            ApiResponseDTO response = new ApiResponseDTO(meta, isValid);
            System.out.println("🔐 [LOGIN] ✅ Validación completada exitosamente");
            return response;

        } catch (Exception e) {
            System.out.println("🔐 [LOGIN] ❌ Error durante la validación: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

}
