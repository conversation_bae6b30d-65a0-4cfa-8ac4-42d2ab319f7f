package com.coppel.controllers;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.coppel.clients.PersonalClient;
import com.coppel.dto.ApiResponseDTO;
import com.coppel.services.LoginService;
import com.coppel.util.Meta;
import com.fasterxml.jackson.core.JsonProcessingException;

@RestController
public class LoginController {

    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);
    private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);

    @Autowired
    PersonalClient personalClient;
    @Autowired
    LoginService loginService;

    @PostMapping("/isUserValid")
    public ApiResponseDTO isUserValid(@RequestBody String tokenazuread) throws JsonProcessingException {
        logger.info("🔐 [LOGIN] Iniciando validación de usuario");
        logger.info("🔐 [LOGIN] Token recibido: {}", tokenazuread != null ? "Token presente (longitud: " + tokenazuread.length() + ")" : "Token nulo");

        try {
            logger.info("🔐 [LOGIN] Deserializando JWT...");
            Long employeeId = loginService.deserializeJwt(tokenazuread);
            logger.info("🔐 [LOGIN] Employee ID extraído del JWT: {}", employeeId);

            logger.info("🔐 [LOGIN] Consultando información del empleado en PersonalClient...");
            String employeeData = personalClient.obtenerEmpleadoInfo(employeeId).getData().toString();
            logger.info("🔐 [LOGIN] Datos del empleado obtenidos: {}", employeeData);

            logger.info("🔐 [LOGIN] Validando usuario en base de datos...");
            Boolean isValid = loginService.isUserValid(employeeData);
            logger.info("🔐 [LOGIN] Resultado de validación: {}", isValid);

            ApiResponseDTO response = new ApiResponseDTO(meta, isValid);
            logger.info("🔐 [LOGIN] ✅ Validación completada exitosamente");
            return response;

        } catch (Exception e) {
            logger.error("🔐 [LOGIN] ❌ Error durante la validación: {}", e.getMessage(), e);
            throw e;
        }
    }

}
