package com.coppel.controllers;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.coppel.clients.PersonalClient;
import com.coppel.dto.ApiResponseDTO;
import com.coppel.services.LoginService;
import com.coppel.util.Meta;
import com.fasterxml.jackson.core.JsonProcessingException;

@RestController
public class LoginController {

    private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);

    @Autowired
    PersonalClient personalClient;
    @Autowired
    LoginService loginService;

    @PostMapping("/isUserValid")
    public ApiResponseDTO isUserValid(@RequestBody String tokenazuread) throws JsonProcessingException {
        Long employeeId = loginService.deserializeJwt(tokenazuread);
        String employeeData = personalClient.obtenerEmpleadoInfo(employeeId).getData().toString();
        return new ApiResponseDTO(meta, loginService.isUserValid(employeeData));
    }

}
