package com.coppel.controllers;

import java.time.LocalDateTime;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.coppel.clients.PersonalClient;
import com.coppel.dto.ApiResponseDTO;
import com.coppel.services.LoginService;
import com.coppel.util.Meta;
import com.fasterxml.jackson.core.JsonProcessingException;

@RestController
public class LoginController {

    private static final Logger logger = LoggerFactory.getLogger(LoginController.class); // SLF4J

    private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);

    @Autowired
    PersonalClient personalClient;
    @Autowired
    LoginService loginService;

    @PostMapping("/isUserValid")
    public ApiResponseDTO isUserValid(@RequestBody String tokenazuread) throws JsonProcessingException {
        LocalDateTime fechaActual = LocalDateTime.now();

        String tokenInfo = tokenazuread != null ? "Token presente longitud: " + tokenazuread.length() : "Token nulo";

        // 1. System.out.println - Para Kubernetes stdout
        System.out.println("[STDOUT] [LOGIN] Iniciando validacion de usuario - time: " + fechaActual + " - " + tokenInfo);

        // 2. SLF4J Logger
        logger.info("[SLF4J] [LOGIN] Iniciando validacion de usuario - token info: {}", tokenInfo);

        try {
            // Deserializar JWT
            System.out.println("[STDOUT] [LOGIN] Deserializando JWT");
            logger.info("[SLF4J] [LOGIN] Deserializando JWT");

            Long employeeId = loginService.deserializeJwt(tokenazuread);

            System.out.println("[STDOUT] [LOGIN] Employee ID extraido: " + employeeId);
            logger.info("[SLF4J] [LOGIN] Employee ID extraido: {}", employeeId);

            // Consultar información del empleado
            System.out.println("[STDOUT] [LOGIN] Consultando informacion del empleado en PersonalClient");
            logger.info("[SLF4J] [LOGIN] Consultando informacion del empleado en PersonalClient");

            String employeeData = personalClient.obtenerEmpleadoInfo(employeeId).getData().toString();

            System.out.println("[STDOUT] [LOGIN] Datos del empleado obtenidos - longitud: " + employeeData.length());
            logger.info("[SLF4J] [LOGIN] Datos del empleado obtenidos - longitud: {}", employeeData.length());

            // Validar usuario
            System.out.println("[STDOUT] [LOGIN] Validando usuario en base de datos");
            logger.info("[SLF4J] [LOGIN] Validando usuario en base de datos");

            Boolean isValid = loginService.isUserValid(employeeData);

            System.out.println("[STDOUT] [LOGIN] Resultado de validacion: " + isValid);
            logger.info("[SLF4J] [LOGIN] Resultado de validacion: {}", isValid);

            ApiResponseDTO response = new ApiResponseDTO(meta, isValid);
            return response;

        } catch (Exception e) {
            System.out.println("[STDOUT] [LOGIN] ERROR durante la validacion: " + e.getMessage());
            logger.error("[SLF4J] [LOGIN] ERROR durante la validacion: {}", e.getMessage(), e);
            throw e;
        }
    }

}
