package com.coppel.controllers;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.coppel.dto.ApiResponseDTO;
import com.coppel.services.TipoCambioService;
import com.coppel.util.Meta;

@RestController
public class TipoCambioController {

    private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);
    @Autowired
    TipoCambioService tipoCambioService;

    @GetMapping("/obtenerTipoCambio")
    public ApiResponseDTO obtenerTipoCambio() {
        return new ApiResponseDTO(meta, tipoCambioService.obtenerTipoDeCambio());
    }

}
