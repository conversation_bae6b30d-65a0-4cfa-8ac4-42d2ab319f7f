package com.coppel.controllers;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.coppel.dto.ApiResponseDTO;
import com.coppel.services.TipoCambioService;
import com.coppel.util.Meta;

@RestController
public class TipoCambioController {

    private static final Logger logger = LoggerFactory.getLogger(TipoCambioController.class);
    private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);
    @Autowired
    TipoCambioService tipoCambioService;

    @GetMapping("/obtenerTipoCambio")
    public ApiResponseDTO obtenerTipoCambio() {
        logger.info("[TIPO-CAMBIO] Iniciando obtención de tipo de cambio");
        try {
            String tipoCambio = tipoCambioService.obtenerTipoDeCambio();
            logger.info("[TIPO-CAMBIO] Tipo de cambio obtenido: {}", tipoCambio);
            return new ApiResponseDTO(meta, tipoCambio);
        } catch (Exception e) {
            logger.error("[TIPO-CAMBIO] Error al obtener tipo de cambio: {}", e.getMessage(), e);
            throw e;
        }
    }

}
