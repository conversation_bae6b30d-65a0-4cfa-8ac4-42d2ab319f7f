package com.coppel.controllers;

import java.util.UUID;
import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.coppel.dto.ApiResponseDTO;
import com.coppel.services.TipoCambioService;
import com.coppel.util.Meta;

@RestController
public class TipoCambioController {

    // Tres tipos de loggers
    private static final Logger logger = LoggerFactory.getLogger(TipoCambioController.class); // SLF4J

    private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);
    @Autowired
    TipoCambioService tipoCambioService;

    @GetMapping("/obtenerTipoCambio")
    public ApiResponseDTO obtenerTipoCambio() {
        LocalDateTime fechaActual = LocalDateTime.now();

        // 1. System.out.println - Para Kubernetes stdout
        System.out.println("[STDOUT] [TIPO-CAMBIO] Iniciando obtencion de tipo de cambio - time: " + fechaActual);

        // 2. SLF4J Logger
        logger.info("[SLF4J] [TIPO-CAMBIO] Iniciando obtencion de tipo de cambio");

        try {
            String tipoCambio = tipoCambioService.obtenerTipoDeCambio();

            System.out.println("[STDOUT] [TIPO-CAMBIO] Tipo de cambio obtenido: " + tipoCambio);
            logger.info("[SLF4J] [TIPO-CAMBIO] Tipo de cambio obtenido: {}", tipoCambio);

            return new ApiResponseDTO(meta, tipoCambio);
        } catch (Exception e) {
            System.out.println("[STDOUT] [TIPO-CAMBIO] ERROR al obtener tipo de cambio: " + e.getMessage());
            logger.error("[SLF4J] [TIPO-CAMBIO] ERROR al obtener tipo de cambio: {}", e.getMessage(), e);
            throw e;
        }
    }

}
