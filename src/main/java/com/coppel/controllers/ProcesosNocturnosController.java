package com.coppel.controllers;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.coppel.dto.ApiResponseDTO;
import com.coppel.dto.ProcesoNocturnoDTO;
import com.coppel.services.impl.ProcesoNocturnoServiceImpl;
import com.coppel.util.Meta;

@RestController
@RequestMapping("/procesos-nocturnos")
public class ProcesosNocturnosController {
  private static final Logger logger = LoggerFactory.getLogger(ProcesosNocturnosController.class);

  @Autowired
  ProcesoNocturnoServiceImpl procesoNocturnoService;

  private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);

  @PostMapping("/")
  public ApiResponseDTO ejecutarFuncion(@RequestBody ProcesoNocturnoDTO body) {
    logger.info("[PROCESOS-NOCTURNOS] Iniciando ejecución de función PLD");
    logger.info("[PROCESOS-NOCTURNOS] Función solicitada: {}", body != null ? body.getFuncion() : "null");
    logger.info("[PROCESOS-NOCTURNOS] Parámetros: {}", body != null && body.getParametros() != null ? java.util.Arrays.toString(body.getParametros()) : "sin parámetros");

    try {
      var resultado = procesoNocturnoService.ejecutarFuncionPld(body);
      logger.info("[PROCESOS-NOCTURNOS] Función ejecutada exitosamente. Resultados: {}", resultado != null ? resultado.size() : 0);
      return new ApiResponseDTO(meta, resultado);
    } catch (Exception e) {
      logger.error("[PROCESOS-NOCTURNOS] Error al ejecutar función: {}", e.getMessage(), e);
      throw e;
    }
  }
}
