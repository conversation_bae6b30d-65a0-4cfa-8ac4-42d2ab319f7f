package com.coppel.controllers;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.coppel.dto.ApiResponseDTO;
import com.coppel.dto.ProcesoNocturnoDTO;
import com.coppel.services.impl.ProcesoNocturnoServiceImpl;
import com.coppel.util.Meta;

@RestController
@RequestMapping("/procesos-nocturnos")
public class ProcesosNocturnosController {
  @Autowired
  ProcesoNocturnoServiceImpl procesoNocturnoService;

  private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);

  @PostMapping("/") 
  public ApiResponseDTO ejecutarFuncion(@RequestBody ProcesoNocturnoDTO body) {
    return new ApiResponseDTO(meta, procesoNocturnoService.ejecutarFuncionPld(body));
  }
}
