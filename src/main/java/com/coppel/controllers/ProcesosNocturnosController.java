package com.coppel.controllers;

import java.util.UUID;
import java.time.LocalDateTime;
import java.util.logging.Level;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.coppel.dto.ApiResponseDTO;
import com.coppel.dto.ProcesoNocturnoDTO;
import com.coppel.services.impl.ProcesoNocturnoServiceImpl;
import com.coppel.util.Meta;

@RestController
@RequestMapping("/procesos-nocturnos")
public class ProcesosNocturnosController {
  // Tres tipos de loggers
  private static final Logger logger = LoggerFactory.getLogger(ProcesosNocturnosController.class); // SLF4J
  private java.util.logging.Logger javaLogger = java.util.logging.Logger.getLogger(ProcesosNocturnosController.class.getName()); // Java Util Logging

  @Autowired
  ProcesoNocturnoServiceImpl procesoNocturnoService;

  private final Meta meta = new Meta(UUID.randomUUID().toString(), "Ok", 200);

  @PostMapping("/")
  public ApiResponseDTO ejecutarFuncion(@RequestBody ProcesoNocturnoDTO body) {
    LocalDateTime fechaActual = LocalDateTime.now();
    String funcion = body != null ? body.getFuncion() : "null";
    String parametros = body != null && body.getParametros() != null ? java.util.Arrays.toString(body.getParametros()) : "sin parametros";

    // 1. System.out.println - Para Kubernetes stdout
    System.out.println("[STDOUT] [PROCESOS-NOCTURNOS] Iniciando ejecucion de funcion PLD - time: " + fechaActual + " - funcion: " + funcion);

    // 2. SLF4J Logger
    logger.info("[SLF4J] [PROCESOS-NOCTURNOS] Iniciando ejecucion de funcion PLD - funcion: {} - parametros: {}", funcion, parametros);

    // 3. Java Util Logging
    javaLogger.log(Level.INFO, String.format("[time: %s] [controller: ProcesosNocturnosController] [method: ejecutarFuncion] [action: inicio_ejecucion_funcion] [funcion: %s] [parametros: %s]",
            fechaActual, funcion, parametros));

    try {
      var resultado = procesoNocturnoService.ejecutarFuncionPld(body);
      int totalResultados = resultado != null ? resultado.size() : 0;

      System.out.println("[STDOUT] [PROCESOS-NOCTURNOS] Funcion ejecutada exitosamente - Resultados: " + totalResultados);
      logger.info("[SLF4J] [PROCESOS-NOCTURNOS] Funcion ejecutada exitosamente - Resultados: {}", totalResultados);
      javaLogger.log(Level.INFO, String.format("[time: %s] [controller: ProcesosNocturnosController] [action: funcion_ejecutada] [total_resultados: %s]",
              LocalDateTime.now(), totalResultados));

      return new ApiResponseDTO(meta, resultado);
    } catch (Exception e) {
      System.out.println("[STDOUT] [PROCESOS-NOCTURNOS] ERROR al ejecutar funcion: " + e.getMessage());
      logger.error("[SLF4J] [PROCESOS-NOCTURNOS] ERROR al ejecutar funcion: {}", e.getMessage(), e);
      javaLogger.log(Level.SEVERE, String.format("[time: %s] [controller: ProcesosNocturnosController] [action: error_ejecucion_funcion] [error: %s]",
              LocalDateTime.now(), e.getMessage()), e);
      throw e;
    }
  }
}
