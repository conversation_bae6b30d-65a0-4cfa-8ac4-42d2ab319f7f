package com.coppel.clients;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.coppel.dto.ApiResponseDTO;

@FeignClient(name = "ms-personal", url = "${clients.uriPersonal}")
public interface PersonalClient {

    @GetMapping(value = "/empleadoInfo/{numemp}", produces = "application/json")
    public ApiResponseDTO obtenerEmpleadoInfo(@PathVariable("numemp") Long numemp);
}
