package com.coppel.dto;

import com.coppel.util.Meta;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ApiResponseDTO
 */
@NoArgsConstructor
@Data
public class ApiResponseDTO {

    private Meta meta;
    private Object data;

    // Constructor con logs para rastrear el contenido
    public ApiResponseDTO(Meta meta, Object data) {
        LocalDateTime fechaActual = LocalDateTime.now();

        // Logs para rastrear el contenido de la respuesta
        System.out.println("[STDOUT] [API-RESPONSE] Creando ApiResponseDTO - time: " + fechaActual);
        System.out.println("[STDOUT] [API-RESPONSE] Meta status: " + (meta != null ? meta.getStatus() : "null"));
        System.out.println("[STDOUT] [API-RESPONSE] Meta statusCode: " + (meta != null ? meta.getStatusCode() : "null"));

        if (data != null) {
            String dataType = data.getClass().getSimpleName();
            String dataInfo = "";

            if (data instanceof java.util.List) {
                java.util.List<?> lista = (java.util.List<?>) data;
                dataInfo = "Lista con " + lista.size() + " elementos";
            } else if (data instanceof String) {
                String str = (String) data;
                dataInfo = "String con longitud: " + str.length();
            } else if (data instanceof Boolean) {
                dataInfo = "Boolean: " + data.toString();
            } else {
                dataInfo = "Objeto: " + data.toString();
            }

            System.out.println("[STDOUT] [API-RESPONSE] Data type: " + dataType + " - Info: " + dataInfo);
        } else {
            System.out.println("[STDOUT] [API-RESPONSE] Data es NULL - ATENCION: El parametrizable recibira content vacio");
        }

        this.meta = meta;
        this.data = data;
    }

}
