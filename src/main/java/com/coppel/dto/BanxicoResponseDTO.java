package com.coppel.dto;

import java.util.List;

public class BanxicoResponseDTO {
    private Bmx bmx;

    public Bmx getBmx() {
        return bmx;
    }

    public void setBmx(Bmx bmx) {
        this.bmx = bmx;
    }

    public static class Bmx {
        private List<Serie> series;

        public List<Serie> getSeries() {
            return series;
        }

        public void setSeries(List<Serie> series) {
            this.series = series;
        }
    }

    public static class Serie {
        private List<Dato> datos;

        public List<Dato> getDatos() {
            return datos;
        }

        public void setDatos(List<Dato> datos) {
            this.datos = datos;
        }
    }

    public static class Dato {
        private String fecha;
        private String dato;

        public String getFecha() {
            return fecha;
        }

        public void setFecha(String fecha) {
            this.fecha = fecha;
        }

        public String getDato() {
            return dato;
        }

        public void setDato(String dato) {
            this.dato = dato;
        }
    }
}
