package com.coppel.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CatEmpDTO {
    private String iduEmpleado;
    private String nomEmpleado;
    private String nomEmpapellidopaterno;
    private String nomEmpapellidomaterno;
    private String fecNacimientoempleado;
    private String nomCalleEmp;
    private String nomColoniaEmp;
    private String numCasaEmp;
    private String clvCodigopostalEmp;
    private String iduPuesto;
    private String nomPuesto;
    private String iduCentro;
    private String nomCentro;
    private String nacionalidad;
    private String numTienda;
}
