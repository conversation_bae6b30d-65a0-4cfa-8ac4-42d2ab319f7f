package com.coppel.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CatLQsQDTO {
    private String iduQeq;
    private String nombre;
    private String nomPaterno;
    private String nomMaterno;
    private String clvCurp;
    private String clvRfc;
    private String fecNacimiento;
    private String desLista;
    private String desEstatus;
    private String desDependencia;
    private String desPuesto;
    private String iduDisposicion;
    private String opcCurpok;
    private String iduRelacion;
    private String desParentesco;
    private String desRazonsocial;
    private String desRfcmoral;
    private String clvIssste;
    private String clvImss;
    private String desIngresos;
    private String nomNombrecompleto;
    private String nomApellidos;
    private String desEntidad;
    private String opcGenero;
}
