package com.coppel.repositories;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import com.coppel.dto.BanxicoResponseDTO;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

@Repository
public class TipoCambioRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Transactional
    public void guardarTipoCambio(BanxicoResponseDTO tipoCambio) {
        try {
            // Guardar los datos de banxico en variables
            String fecha = tipoCambio.getBmx().getSeries().get(0).getDatos().get(0).getFecha();
            String valor = tipoCambio.getBmx().getSeries().get(0).getDatos().get(0).getDato();

            // Si no existe ningun registro con la fecha se realiza el insert
            if(checkRepeatedRecordDate(fecha) != 0)
                return;

            // Se realiza el insert cuando checkRepeatedRecordDate obtiene 0 registros
            String queryString = "INSERT INTO TiposDeCambios (nDivisaId, nCambio, dFechaRegistro) " +
                    "VALUES (:divisa, :dato, CONVERT(datetime, :fecha, 103))";

            Query query = entityManager.createNativeQuery(queryString);
            query.setParameter("divisa", 3);
            query.setParameter("dato", valor);
            query.setParameter("fecha", fecha);
            query.executeUpdate();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al insertar tipo de cambio", e);
        }
    }

    public Long checkRepeatedRecordDate(String recordDate) {
        try {
            // Consulta para verificar si ya existe un registro con la misma fecha
            String checkQueryString = "SELECT COUNT(*) FROM TiposDeCambios WHERE dFechaRegistro = CONVERT(datetime, :fecha, 103)";
            Query checkQuery = entityManager.createNativeQuery(checkQueryString);
            checkQuery.setParameter("fecha", recordDate);
            
            // Obtenemos el resultado de la consulta
            return ((Number) checkQuery.getSingleResult()).longValue();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al validar fecha registro", e);
        }
    }

}
