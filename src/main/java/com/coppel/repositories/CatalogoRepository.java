package com.coppel.repositories;

import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import com.coppel.dto.CatEmpDTO;
import com.coppel.dto.CatLQsQDTO;
import com.coppel.dto.CatSHCPDTO;
import com.coppel.dto.CatalogoDTO;
import com.coppel.mappers.CatalogoMapper;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

@Repository
public class CatalogoRepository {

    @PersistenceContext
    private EntityManager entityManager;

    private String formatoFecha = "yyyyMMdd";
    private String values = "VALUES ( ";

    @Transactional
    public List<CatalogoDTO> obtenerCats() {
        java.time.LocalDateTime fechaActual = java.time.LocalDateTime.now();

        // Logs detallados para rastrear el problema
        System.out.println("[STDOUT] [CATALOGO-REPOSITORY] Iniciando obtenerCats - time: " + fechaActual);

        try {
            // Obtener campos CatalogosFechaActualizacion
            String querySelect = "SELECT id, catalogo, fecha, isActive " +
                    "from CatalogosFechaActualizacion";

            System.out.println("[STDOUT] [CATALOGO-REPOSITORY] Query a ejecutar: " + querySelect);

            Query query = entityManager.createNativeQuery(querySelect);

            System.out.println("[STDOUT] [CATALOGO-REPOSITORY] Query creada, ejecutando getResultList...");

            @SuppressWarnings("unchecked")
            List<Object> resultado = query.getResultList();

            System.out.println("[STDOUT] [CATALOGO-REPOSITORY] Resultado obtenido - Total registros: " + resultado.size());

            if (resultado.isEmpty()) {
                System.out.println("[STDOUT] [CATALOGO-REPOSITORY] ATENCION: La consulta no devolvio registros - tabla vacia");
            }

            List<CatalogoDTO> catalogosMapeados = resultado.stream().map(CatalogoMapper::mapCatalogoDTO).toList();

            System.out.println("[STDOUT] [CATALOGO-REPOSITORY] Mapeo completado - Total DTOs: " + catalogosMapeados.size());

            return catalogosMapeados;
        } catch (Exception e) {
            System.out.println("[STDOUT] [CATALOGO-REPOSITORY] ERROR en obtenerCats: " + e.getClass().getSimpleName() + " - " + e.getMessage());

            // Verificar si es error de tabla no encontrada
            if (e.getMessage() != null && e.getMessage().toLowerCase().contains("not found")) {
                System.out.println("[STDOUT] [CATALOGO-REPOSITORY] ERROR CRITICO: Tabla CatalogosFechaActualizacion no existe en la BD");
                System.out.println("[STDOUT] [CATALOGO-REPOSITORY] SOLUCION: Crear la tabla o cambiar la configuracion de BD");
            }

            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al obtener catalogos", e);
        }
    }

    @Transactional
    public CatalogoDTO actualizarFechaCat(Long idCat) {
        try {
            String queryUPD = "UPDATE CatalogosFechaActualizacion SET fecha = :fecha where id = :id";
            Query query = entityManager.createNativeQuery(queryUPD);

            String todaysDate = obtenerFechaHoy();

            query.setParameter("fecha", todaysDate);
            query.setParameter("id", idCat);

            query.executeUpdate();

            // Obtener campos CatalogosFechaActualizacion
            String queryStrngSelect = "SELECT id, catalogo, fecha, isActive from CatalogosFechaActualizacion where id = :id";
            Query querySelect = entityManager.createNativeQuery(queryStrngSelect);

            querySelect.setParameter("id", idCat);

            Object resultado = querySelect.getSingleResult();
            return CatalogoMapper.mapCatalogoDTO(resultado);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al actualizar fecha catalogo", e);
        }
    }

    public String obtenerFechaHoy() {
        ZoneId zonaHorariaLocal = ZoneId.systemDefault();
        LocalDate fechaActual = LocalDate.now(zonaHorariaLocal);
        DateTimeFormatter formateador = DateTimeFormatter.ofPattern(formatoFecha);
        return fechaActual.format(formateador);
    }

    @Transactional
    public Boolean actualizaLNQsQRespaldo() {
        try {
            // Elimar tabla LNQsQRespaldo
            comprobarExistenciaTablaDrop("ListaNegraQuienEsQuien_respaldo");

            // Pasar LNQsQ a LNQsQRespaldo
            String queryRestore = "SELECT * INTO ListaNegraQuienEsQuien_respaldo from ListaNegraQuienEsQuien";
            entityManager.createNativeQuery(queryRestore).executeUpdate();

            // Vaciar tabla LNQsQCopia
            String queryDrop = "DELETE FROM ListaNegraQuienEsQuien_copia";
            entityManager.createNativeQuery(queryDrop).executeUpdate();

            return true;
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Error al respaldar lista negra quien es quien", e);
        }
    }

    @Transactional
    public Boolean pasarCatLQsQHaciaLNQsQCopia() {
        try {
            String queryStrng = "insert into ListaNegraQuienEsQuien_copia select " +
                    " idu_qeq, nom_persona, nom_paterno, nom_materno, clv_curp, clv_rfc, fec_nacimiento, des_lista, des_estatus, "
                    +
                    " des_dependencia, des_puesto, idu_disposicion, opc_curpok, idu_relacion, des_parentesco, des_razonsocial, "
                    +
                    " des_rfcmoral, clv_issste, clv_imss, des_ingresos, nom_nombrecompleto, nom_apellidos, des_entidad, opc_genero, nom_nombreconcatenado "
                    +
                    "from cat_listadoquienesquien";

            Query query = entityManager.createNativeQuery(queryStrng);
            query.executeUpdate();

            return true;

        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Error al respaldar lista negra quien es quien copia", e);
        }
    }

    @Transactional
    public boolean areCountsEqual() {
        try {
            String queryStr = "SELECT CASE " +
                    "WHEN (SELECT COUNT(*) FROM ListaNegraQuienEsQuien_copia) = " +
                    "(SELECT COUNT(*) FROM cat_listadoquienesquien) " +
                    "THEN 1 " +
                    "ELSE 0 " +
                    "END";

            Query query = entityManager.createNativeQuery(queryStr);
            Integer result = (Integer) query.getSingleResult();
            return result == 1;
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al comparar tablas", e);
        }
    }

    @Transactional
    public void eliminarLNQsQ() {
        try {
            // Vaciar tabla LNQsQCopia
            String queryDrop = "DELETE FROM ListaNegraQuienEsQuien";
            entityManager.createNativeQuery(queryDrop).executeUpdate();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al eliminar tabla LNQsQ", e);
        }
    }

    @Transactional
    public void guardarLNQsQ() {
        try {
            String queryInsert = "insert into ListaNegraQuienEsQuien select " +
                    "idqeq, nombre, paterno, materno, curp, rfc, fecnac, lista, estatus, dependencia, " +
                    "puesto, iddispo, curpok, idrel, parentesco, razonsoc, rfcmoral, issste, imss, " +
                    "ingresos, nomcomp, apellidos, entidad, genero, clavecoppel, getdate() " +
                    "from ListaNegraQuienEsQuien_copia";

            Query query = entityManager.createNativeQuery(queryInsert);
            query.executeUpdate();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al guardar en LNQsQ", e);
        }
    }

    @Transactional
    public Boolean crearNuevaCatLNQsQ() {
        try {
            // Obtener la fecha hoy
            String todaysDate = obtenerFechaHoy();
            String tableName = "cat_listadoquienesquien_" + todaysDate;

            comprobarExistenciaTablaDrop(tableName);

            String queryInsert = "select * into cat_listadoquienesquien_" + todaysDate
                    + " from cat_listadoquienesquien";

            Query query = entityManager.createNativeQuery(queryInsert);
            query.executeUpdate();

            return true;

        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Error al crear catálogo quien es quien", e);
        }
    }

    public void comprobarExistenciaTablaDrop(String tableName) {
        try {
            // Comprobar si la tabla existe
            String checkTableExistsQuery = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '"
                    + tableName + "'";
            Query checkQuery = entityManager.createNativeQuery(checkTableExistsQuery);
            Number tableCount = (Number) checkQuery.getSingleResult();

            // Si la tabla existe, eliminarla
            if (tableCount.intValue() > 0) {
                String dropTableQuery = "DROP TABLE " + tableName;
                Query dropQuery = entityManager.createNativeQuery(dropTableQuery);
                dropQuery.executeUpdate();
            }
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al eliminar tabla", e);
        }
    }

    private boolean tablaExiste(String tableName) {
        try {
            String checkTableQuery = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = :tableName";
            Number count = (Number) entityManager.createNativeQuery(checkTableQuery)
                    .setParameter("tableName", tableName)
                    .getSingleResult();
            return count.intValue() > 0;

        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Error al comprobar existencia de la tabla", e);
        }
    }

    @Transactional
    public void guardarArchivoQsQ(CatLQsQDTO currentCatQsQ) {
        try {
            String queryInsert = "insert into cat_listadoquienesquien " +
                    "( " +
                    "idu_qeq, nom_persona, nom_paterno, nom_materno, " +
                    "clv_curp, clv_rfc, fec_nacimiento, des_lista, des_estatus, des_dependencia, " +
                    "des_puesto, idu_disposicion, opc_curpok, idu_relacion, des_parentesco, des_razonsocial, des_rfcmoral, clv_issste, "
                    +
                    "clv_imss, des_ingresos, nom_nombrecompleto, nom_apellidos, des_entidad, opc_genero " +
                    ") " +
                    values +
                    ":idu_qeq, :nombre, :nom_paterno, :nom_materno, " +
                    ":clv_curp, :clv_rfc, :fec_nacimiento, :des_lista, :des_estatus, :des_dependencia, "
                    +
                    ":des_puesto, :idu_disposicion, :opc_curpok, :idu_relacion, :des_parentesco, :des_razonsocial, :des_rfcmoral, :clv_issste, "
                    +
                    ":clv_imss, :des_ingresos, :nom_nombrecompleto, :nom_apellidos, :des_entidad, :opc_genero" +
                    ")";
            Query query = entityManager.createNativeQuery(queryInsert);
            query = setQueryParameters(query, CatLQsQDTO.class, currentCatQsQ);
            query.executeUpdate();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al guardar archivo QsQ", e);
        }

    }

    @Transactional
    public void limpiarCatQsQ() {
        try {
            // Vaciar tabla catQsQ
            String queryTrunc = "truncate table cat_listadoquienesquien";
            entityManager.createNativeQuery(queryTrunc).executeUpdate();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al vaciar tabla CatQsQ", e);
        }

    }

    @Transactional
    public Boolean guardarPaisesBloqueadosQsQ() {
        try {
            // Obtener fecha actual
            String todaysDate = obtenerFechaHoy();
            // Nombre de la tabla a comprobar datos
            String tableName = "cat_paisesbloqueadosqeq_" + todaysDate;
            // Si ya existe tabla dropearla
            comprobarExistenciaTablaDrop(tableName);

            // Realizar respaldo
            String queryRespaldo = "select * into cat_paisesbloqueadosqeq_" + todaysDate
                    + " from cat_paisesbloqueadosqeq";
            entityManager.createNativeQuery(queryRespaldo).executeUpdate();

            // Vaciar datos tabla cat_paisesbloqueadosqeq
            String queryTruncate = "TRUNCATE TABLE cat_paisesbloqueadosqeq";
            entityManager.createNativeQuery(queryTruncate).executeUpdate();

            // Insertar nuevos datos
            String queryInsert = "INSERT INTO cat_paisesbloqueadosqeq ( " +
                    "idu_paisqeq, nom_paisqeq, clv_paiscoppel, des_listaqeq, " +
                    "opc_estatus, fec_registro, flag " +
                    ") " +
                    "SELECT " +
                    "LTRIM(RTRIM(idu_qeq)) AS idu_qeq, " +
                    "REPLACE(COALESCE(UPPER(LTRIM(RTRIM(nom_persona))), ''), '''', '') AS nom_persona, " +
                    "'', " +
                    "LTRIM(RTRIM(des_lista)) AS des_lista, " +
                    "1, " +
                    "GETDATE(), " +
                    "1 " +
                    "FROM " +
                    "cat_listadoquienesquien " +
                    "WHERE " +
                    "LTRIM(RTRIM(des_lista)) IN ('GAFI', 'OCDE', 'REFIPRE') ";
            entityManager.createNativeQuery(queryInsert).executeUpdate();

            return true;
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al guardar paises bloqueados", e);
        }
    }

    @Transactional
    public Boolean respaldoSHCP() {
        try {
            // Obtener la fecha hoy
            String todaysDate = obtenerFechaHoy();
            String tableName = "cat_listadoshcp" + todaysDate;
            String tableCatListado = "cat_listadoshcp_respaldo";

            comprobarExistenciaTablaDrop(tableName);

            String queryInsert = "select * into cat_listadoshcp" + todaysDate
                    + " from cat_listadoshcp";

            // RESPALDAMOS LA TABLA cat_listadoshcp A LA cat_listadoshcpAAAAMMDD
            entityManager.createNativeQuery(queryInsert).executeUpdate();

            tableName = tableCatListado + todaysDate;

            comprobarExistenciaTablaDrop(tableName);

            String queryInsertRespaldo = "select * into cat_listadoshcp_respaldo" + todaysDate
                    + " from cat_listadoshcp_respaldo";

            // RESPALDAR LA TABLA cat_listadoshcp_respaldo A LA
            // cat_listadoshcp_respaldoAAAAMMDD
            if (tablaExiste(tableCatListado)) {
                entityManager.createNativeQuery(queryInsertRespaldo).executeUpdate();
            }

            // Elimar tabla cat_listadoshcp_respaldo
            comprobarExistenciaTablaDrop(tableCatListado);

            String queryTruncate = "TRUNCATE TABLE cat_listadoshcp";
            // Vaciar tabla cat_listadoshcp
            entityManager.createNativeQuery(queryTruncate).executeUpdate();

            return true;

        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Error al respaldar SHCP", e);
        }
    }

    @Transactional
    public void guardarArchivoSHCP(CatSHCPDTO currentCat) {
        try {
            String queryInsert = "insert into cat_listadoshcp " +
                    "( " +
                    "nom_primernombre, nom_segundonombre, nom_primerapellido, nom_segundoapellido, " +
                    "fec_nacimiento, fec_registro " +
                    ") " +
                    values +
                    ":nom_primernombre, :nom_segundonombre, :nom_primerapellido, :nom_segundoapellido, " +
                    ":fec_nacimiento, :fec_registro " +
                    ")";
            Query query = entityManager.createNativeQuery(queryInsert);
            query = setQueryParameters(query, CatSHCPDTO.class, currentCat);
            query.executeUpdate();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al guardar archivo shcp", e);
        }

    }

    @Transactional
    public Boolean respaldoCatListadoSHCP() {
        try {
            String queryInsert = "select * into cat_listadoshcp_respaldo from cat_listadoshcp";

            Query query = entityManager.createNativeQuery(queryInsert);
            query.executeUpdate();

            return true;
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al realizar respaldo shcp", e);
        }
    }

    @Transactional
    public void guardarArchivoEMP(CatEmpDTO currentCat) {
        try {
            String queryInsert = "insert into cat_empleados_coppel " +
                    "( " +
                    "idu_empleado, nom_empleado, nom_empapellidopaterno, nom_empapellidomaterno, " +
                    "fec_nacimientoempleado, nom_calle_emp, nom_colonia_emp, num_casa_emp, clv_codigopostal_emp, " +
                    "idu_puesto, nom_puesto, idu_centro, nom_centro, nacionalidad, num_tienda " +
                    ") " +
                    values +
                    ":idu_empleado, :nom_empleado, :nom_empapellidopaterno, :nom_empapellidomaterno, " +
                    ":fec_nacimientoempleado, :nom_calle_emp, :nom_colonia_emp, :num_casa_emp, :clv_codigopostal_emp, " +
                    ":idu_puesto, :nom_puesto, :idu_centro, :nom_centro, :nacionalidad, :num_tienda " +
                    ")";
            Query query = entityManager.createNativeQuery(queryInsert);
            query = setQueryParameters(query, CatEmpDTO.class, currentCat);
            query.executeUpdate();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al guardar archivo empleados", e);
        }
    }

    public Query setQueryParameters(Query query, Class<?> clazz, Object instance) {
        Method[] methods = clazz.getMethods();
        try {
            for (Method method : methods) {
                if (method.getName().startsWith("get") && method.getParameterCount() == 0) {
                    String fieldName = method.getName().substring(3, 4).toLowerCase(Locale.ROOT)
                            + method.getName().substring(4);
                    if (!fieldName.equals("class")) {
                        String transformedFieldName = transformFieldName(fieldName);
                        query.setParameter(transformedFieldName, method.invoke(instance));
                    }
                }
            }
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al realizar query", e);
        }
        return query;
    }

    public String transformFieldName(String fieldName) {
        StringBuilder result = new StringBuilder();
        for (char c : fieldName.toCharArray()) {
            if (Character.isUpperCase(c)) {
                result.append('_').append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    @Transactional
    public Boolean respaldoEmp() {
        try {
            // Obtener la fecha hoy
            String todaysDate = obtenerFechaHoy();
            String tableName = "cat_empleados_Coppel" + todaysDate;

            comprobarExistenciaTablaDrop(tableName);

            String queryInsert = "select * into cat_empleados_Coppel" + todaysDate
                    + " from cat_empleados_Coppel";

            // RESPALDAMOS LA TABLA cat_empleados_Coppel A LA cat_empleados_CoppelAAAAMMDD
            entityManager.createNativeQuery(queryInsert).executeUpdate();

            String queryTruncate = "TRUNCATE TABLE cat_empleados_Coppel";
            // Vaciar tabla cat_empleados_Coppel
            entityManager.createNativeQuery(queryTruncate).executeUpdate();

            return true;

        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Error al respaldar tabla empleados", e);
        }
    }

    public Number obtenerRegistrosPorCatalogo(String tableName) {
        try {
            // Comprobar si la tabla existe
            String checkTableExistsQuery = getQueryCountTable(tableName);
            Query checkQuery = entityManager.createNativeQuery(checkTableExistsQuery);

            return (Number) checkQuery.getSingleResult();
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Error al eliminar tabla", e);
        }
    }

    public String getQueryCountTable(String tableName) {
        String query = "";
        switch (tableName) {
            case "cat_listadoquienesquien":
                query = "SELECT COUNT(*) FROM cat_listadoquienesquien";
                break;
            case "cat_listadoshcp":
                query = "SELECT COUNT(*) FROM cat_listadoshcp";
                break;
            case "cat_empleados_coppel":
                query = "SELECT COUNT(*) FROM cat_empleados_coppel";
                break;
            default:
                break;
        }
        return query;
    }

}
