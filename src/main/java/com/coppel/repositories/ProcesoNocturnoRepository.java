package com.coppel.repositories;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;
import org.springframework.web.server.ResponseStatusException;

import jakarta.persistence.EntityManager;
import jakarta.persistence.ParameterMode;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.StoredProcedureQuery;
import jakarta.transaction.Transactional;

import com.coppel.dto.ProcesoNocturnoDTO;

@Repository
public class ProcesoNocturnoRepository {
  @PersistenceContext
  private EntityManager entityManager;

  @Transactional
  public List<Object[]> ejecutarFuncionPld(ProcesoNocturnoDTO proceso) {
    java.time.LocalDateTime fechaActual = java.time.LocalDateTime.now();
    String funcion = proceso != null ? proceso.getFuncion() : "null";

    System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Iniciando ejecutarFuncionPld - time: " + fechaActual);
    System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Funcion a ejecutar: " + funcion);

    try {
      System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Validando funcion...");
      boolean esValido = validarFuncion(proceso.getFuncion());

      if (!esValido) {
        System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] ERROR: Funcion no valida - " + funcion);
        throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "La función no está permitida para ejecución o no existe");
      }

      System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Funcion validada correctamente");
      System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Creando StoredProcedureQuery...");

      StoredProcedureQuery query = entityManager.createStoredProcedureQuery(proceso.getFuncion());
      String[] parametros = proceso.getParametros();

      if (parametros != null) {
        System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Registrando " + parametros.length + " parametros");
        for (int i = 0; i < parametros.length; i ++) {
          query.registerStoredProcedureParameter(i, String.class, ParameterMode.IN);
          query.setParameter(i, parametros[i]);
          System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Parametro " + i + ": " + parametros[i]);
        }
      } else {
        System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Sin parametros para la funcion");
      }

      System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Ejecutando query.execute()...");
      List<Object[]> results = null;
      if (query.execute()) {
        System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Query ejecutada, obteniendo resultados...");
        results = query.getResultList();

        if (results != null) {
          System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Resultados obtenidos - Total: " + results.size());
          if (!results.isEmpty()) {
            System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Primer resultado: " + java.util.Arrays.toString(results.get(0)));
          }
        } else {
          System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] ATENCION: getResultList() devolvio NULL");
        }
      } else {
        System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] ATENCION: query.execute() devolvio false - sin resultados");
      }

      System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] Retornando resultados - Total: " + (results != null ? results.size() : "null"));
      return results;
    } catch (Exception e) {
      System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] ERROR al ejecutar funcion: " + e.getClass().getSimpleName() + " - " + e.getMessage());
      System.out.println("[STDOUT] [PROCESO-NOCTURNO-REPOSITORY] ATENCION: Se lanzara ResponseStatusException - causara data vacio en parametrizable");
      if (e instanceof ResponseStatusException) throw e;
      throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error al ejecutar la función", e);
    }
  }

  private boolean validarFuncion(String nombreFuncion) {
    try {
      String criterio = "SELECT nom_funcion as funcion, null as parametros FROM cat_procedimientos_permitidos_ms_pld WHERE nom_funcion = :nombreFuncion";
      Query query = entityManager.createNativeQuery(criterio, ProcesoNocturnoDTO.class);
      query.setParameter("nombreFuncion", nombreFuncion);
      List<ProcesoNocturnoDTO> procesosPermitidos = query.getResultList();
      return procesosPermitidos.size() != 0;
    } catch (Exception e) {
      return false;
    }
  }
}
