package com.coppel.repositories;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;
import org.springframework.web.server.ResponseStatusException;

import jakarta.persistence.EntityManager;
import jakarta.persistence.ParameterMode;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.StoredProcedureQuery;
import jakarta.transaction.Transactional;

import com.coppel.dto.ProcesoNocturnoDTO;

@Repository
public class ProcesoNocturnoRepository {
  @PersistenceContext
  private EntityManager entityManager;

  @Transactional
  public List<Object[]> ejecutarFuncionPld(ProcesoNocturnoDTO proceso) {
    try {
      boolean esValido = validarFuncion(proceso.getFuncion());
      if (!esValido) throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "La función no está permitida para ejecución o no existe");

      StoredProcedureQuery query = entityManager.createStoredProcedureQuery(proceso.getFuncion());
      String[] parametros = proceso.getParametros();
      
      if (parametros != null) {
        for (int i = 0; i < parametros.length; i ++) {
          query.registerStoredProcedureParameter(i, String.class, ParameterMode.IN);
          query.setParameter(i, parametros[i]);
        }
      }

      List<Object[]> results = null;
      if (query.execute()) {
        results = query.getResultList();
      }

      return results;
    } catch (Exception e) {
      if (e instanceof ResponseStatusException) throw e;
      throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error al ejecutar la función", e);
    }
  }

  private boolean validarFuncion(String nombreFuncion) {
    try {
      String criterio = "SELECT nom_funcion as funcion, null as parametros FROM cat_procedimientos_permitidos_ms_pld WHERE nom_funcion = :nombreFuncion";
      Query query = entityManager.createNativeQuery(criterio, ProcesoNocturnoDTO.class);
      query.setParameter("nombreFuncion", nombreFuncion);
      List<ProcesoNocturnoDTO> procesosPermitidos = query.getResultList();
      return procesosPermitidos.size() != 0;
    } catch (Exception e) {
      return false;
    }
  }
}
