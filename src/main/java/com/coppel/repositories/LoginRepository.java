package com.coppel.repositories;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;
import org.springframework.web.server.ResponseStatusException;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

@Repository
public class LoginRepository {
    @PersistenceContext
    private EntityManager entityManager;

    public Boolean isUserValid(Long centerId) {
        try {
            // Pasar LNQsQ a LNQsQRespaldo
            String queryRestore = "SELECT centro from centrosValidosUsuarios where centro = :centerId";
            Query query = entityManager.createNativeQuery(queryRestore);
            query.setParameter("centerId", centerId);
            Long centerIdfromDB = ((Number) query.getSingleResult()).longValue();
            return centerId.equals(centerIdfromDB);
        } catch (Exception e) {
             throw new ResponseStatusException(HttpStatus.CONFLICT, "Error al obtener centros validos", e);
        }
    }
}
