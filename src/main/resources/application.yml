server:
  servlet:
    context-path: /ms-pld/api/v1
  port: 8080

app:
  authUri: https://juridicosajv3.coppel.space/ms-login-homologado/api/v1/exp/token/validar-token
  ignoreSession: true
  allowedOrigins: "*"
  allowedMethods: GET, POST, PATCH, PUT, DELETE, OPTIONS, HEAD
  allowedHeaders: Access-Control-Allow-Origin,Access-Control-Allow-Headers,Access-Control-Allow-Methods,Accept,Authorization,Content-Type,Method,Origin,X-Forwarded-For,X-Real-IP,IdP
  exposedHeaders: X-Get-Header
  allowedPaths: /actuator
clients:
  uriBanxico: https://www.banxico.org.mx/SieAPIRest/service/v1/series/SF60653/datos/
  uriPersonal: http://localhost:8091/api/v1/personal-linea
  bmxToken: efd27d5b09d62bc37da9791a2e6a5cc000b66c14ef3808327195f351bf0df6fd
proxy:
  host: **********
  port: 3128
  isRequired: false
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
    tomcat:
      connection-properties: useUnicode=true;characterEncoding=utf-8;
    hikari:
      minimumIdle: 4
      maximumPoolSize: 8
      idleTimeout: 15000
      poolName: SpringBootJPAHikariCP
      maxLifetime: 1800000
      connectionTimeout: 30000
  sql:

  
    init:
      encoding: UTF-8
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  h2:
    console:
      enabled: true
  servlet:
    multipart:
      max-file-size: 250MB
      max-request-size: 250MB
  jackson:
    time-zone: America/Mazatlan

