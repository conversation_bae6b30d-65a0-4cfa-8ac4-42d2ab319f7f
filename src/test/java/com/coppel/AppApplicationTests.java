package com.coppel;

import static org.junit.jupiter.api.Assertions.assertNotEquals;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.coppel.config.AppConfig;

@SpringBootTest
class AppApplicationTests {
	@Autowired
	private AppConfig config;

	@Test
    void getFacturasStatusCode() throws Exception {
        String contextVersion = config.getBuildProperties().getVersion();

        
        assertNotEquals(null, contextVersion);
    }
}
