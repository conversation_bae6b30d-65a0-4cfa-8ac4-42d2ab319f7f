/**
 * Copyright (c) 2023, <PERSON><PERSON> y/o sus afiliados. Todos los derechos reservados.
 *
 */

package capaDePresentacion;

import capaDeDatos.ConexionCD;
import capaDeLogicaDeNegocios.LogCLN;
import capaDeLogicaDeNegocios.ProcesoCLN;
//import capaDeLogicaDeNegocios.SambaCLN;
import capaDeLogicaDeNegocios.SftpCLN;
import constantes.generalConstantes;
import utilidades.JasyptEncriptacion;

public class Main {

	public static void main(String[] args) throws Exception {

		LogCLN log = new LogCLN();
		ConexionCD conexion = new ConexionCD();

		// Validacion de argumentos
		if (args.length == 3) {
			ProcesoCLN proceso = new ProcesoCLN(Integer.parseInt(args[0]), args[1], args[2]);

			// Validacion de conexion a BD
			if (conexion.validarConexiones()) {

				// Validar proceso existente
				if (proceso.validarProceso()) {
					proceso.iniciarProceso();
				}
			} else {
				log.grabarLog(generalConstantes.MENSAJE_SIN_CONEXION_A_BD);
			}
		// Opcion para poder encriptar cadenas
		} else if (args.length == 1) {
			JasyptEncriptacion encriptador = new JasyptEncriptacion();
			System.out.println(encriptador.cifrar(args[0]));
		} else if (args.length == 7) {
			// Opción para poder ejecutarse desde el servicio node.
			// Ya no se utilizará esta funcionalidad, no se elimina en caso que se necesite a futuro.
			// SambaCLN.bajarArchivoServidor(args[0], args[1], args[2], args[3], args[4], args[5], args[6], null);
		} else if (args.length == 8) {
			// Opción para poder ejecutarse desde el servicio node.
			SftpCLN.subirArchivoServidor(args[0], args[1], args[2], args[3], args[4], args[5], args[6], args[7], null, 22);
		} else {
			log.grabarLog(generalConstantes.MENSAJE_CANTIDAD_PARAMETROS_ERRONEA);
		}
	}
}
