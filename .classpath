<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry excluding="src/" kind="src" path=""/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="lib/ant-1.10.14.jar"/>
	<classpathentry kind="lib" path="lib/ant-launcher-1.10.14.jar"/>
	<classpathentry kind="lib" path="lib/commons-codec-1.17.0.jar"/>
	<classpathentry kind="lib" path="lib/commons-collections4-4.4.jar"/>
	<classpathentry kind="lib" path="lib/commons-compress-1.27.1.jar"/>
	<classpathentry kind="lib" path="lib/commons-io-2.16.1.jar"/>
	<classpathentry kind="lib" path="lib/commons-lang3-3.13.0.jar"/>
	<classpathentry kind="lib" path="lib/commons-logging-1.2.jar"/>
	<classpathentry kind="lib" path="lib/commons-math3-3.6.1.jar"/>
	<classpathentry kind="lib" path="lib/commons-text-1.10.0.jar"/>
	<classpathentry kind="lib" path="lib/curvesapi-1.08.jar"/>
	<classpathentry kind="lib" path="lib/itextpdf-********.jar"/>
	<classpathentry kind="lib" path="lib/jackson-annotations-2.16.1.jar"/>
	<classpathentry kind="lib" path="lib/jackson-core-2.16.1.jar"/>
	<classpathentry kind="lib" path="lib/jackson-databind-2.16.1.jar"/>
	<classpathentry kind="lib" path="lib/javax.activation-1.2.0.jar"/>
	<classpathentry kind="lib" path="lib/jsch-0.1.55.jar"/>
	<classpathentry kind="lib" path="lib/mssql-jdbc-12.4.2.jre11.jar"/>
	<classpathentry kind="lib" path="lib/poi-5.4.1.jar"/>
	<classpathentry kind="lib" path="lib/poi-ooxml-5.4.1.jar"/>
	<classpathentry kind="lib" path="lib/poi-excelant-5.4.1.jar"/>
	<classpathentry kind="lib" path="lib/poi-ooxml-full-5.4.1.jar"/>
	<classpathentry kind="lib" path="lib/poi-ooxml-lite-5.4.1.jar"/>
	<classpathentry kind="lib" path="lib/postgresql-42.6.2.jar"/>
	<classpathentry kind="lib" path="lib/xmlbeans-5.2.1.jar"/>
	<classpathentry kind="lib" path="lib/jasypt-1.9.3.jar"/>
	<classpathentry kind="lib" path="lib/log4j-api-2.24.3.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
