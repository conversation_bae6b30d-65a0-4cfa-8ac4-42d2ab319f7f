[{"textPayload": "[dd.trace 2025-06-30 08:46:57:671 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "hxo4kqbvzc5pany1", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T14:46:57.672401143Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T14:47:14.869107792Z"}, {"textPayload": "[dd.trace 2025-06-30 08:49:23:946 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "7au31s9rpe1ioqzf", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "location": "us-central1", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T14:49:23.946834027Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T14:49:28.759298230Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "yt8kn5cunooh8ttx", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T14:49:23.947346057Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T14:49:28.759298230Z"}, {"textPayload": "[dd.trace 2025-06-30 08:52:23:663 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "wiutxfm1ovrxg75i", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T14:52:23.664289588Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T14:52:38.906437579Z"}, {"textPayload": "[dd.trace 2025-06-30 08:55:38:904 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "aj8eaa289djvcdxs", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "location": "us-central1"}}, "timestamp": "2025-06-30T14:55:38.904442399Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T14:55:44.170945820Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "21ehdbw3lbdzljhh", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T14:55:38.904522350Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T14:55:44.170945820Z"}, {"textPayload": "[dd.trace 2025-06-30 08:57:27:562 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "8iu9ibax94ad4d7u", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T14:57:27.563164990Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T14:57:44.862854848Z"}, {"textPayload": "[dd.trace 2025-06-30 09:00:43:828 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "be179nkcvp7kfnnw", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T15:00:43.828948262Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:00:48.772977100Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "0m42w6ikn1782jef", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1"}}, "timestamp": "2025-06-30T15:00:43.828985492Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:00:48.772977100Z"}, {"textPayload": "[dd.trace 2025-06-30 09:02:39:672 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "1t3eoltfe06mtzhi", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T15:02:39.672422569Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:02:59.883712245Z"}, {"textPayload": "[dd.trace 2025-06-30 09:05:43:932 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "uazl2n2d8yxls9s7", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T15:05:43.932559282Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:05:48.956320316Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "gsa3kpo8lveb13v3", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T15:05:43.933187951Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:05:48.956320316Z"}, {"textPayload": "[dd.trace 2025-06-30 09:07:45:567 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "pvmddhkmxhwsngrk", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T15:07:45.568045649Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:08:04.367904798Z"}, {"textPayload": "[dd.trace 2025-06-30 09:10:48:839 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "rlskfibriizsj25j", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T15:10:48.840019225Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:10:53.752701394Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "570xx4oj2e1potu7", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T15:10:48.840067625Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:10:53.752701394Z"}, {"textPayload": "[dd.trace 2025-06-30 09:12:57:671 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "wec6ha8ix0u55jy7", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T15:12:57.672246782Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:13:14.868442142Z"}, {"textPayload": "[dd.trace 2025-06-30 09:15:48:867 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "hqlww3jyhppypye2", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T15:15:48.868280929Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:15:53.843836631Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "k4eow0p58rm7ayes", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T15:15:48.868333149Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:15:53.843836631Z"}, {"textPayload": "[dd.trace 2025-06-30 09:17:59:569 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "kog9qz5bl566to0d", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T15:17:59.569413217Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:18:19.371297201Z"}, {"textPayload": "[dd.trace 2025-06-30 09:21:13:910 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "b9xj51ro7ulowdbk", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T15:21:13.911084454Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:21:18.825883811Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "j0j0u2wcyolhe6ol", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T15:21:13.911340164Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:21:18.825883811Z"}, {"textPayload": "[dd.trace 2025-06-30 09:23:01:563 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "k8j5qffnvd9ic5k7", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T15:23:01.563811212Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:23:19.364950910Z"}, {"textPayload": "[dd.trace 2025-06-30 09:26:13:972 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "v1v1lvstzbpjgje6", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T15:26:13.973242562Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:26:19.682200535Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "jjscbynvyby07ww2", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T15:26:13.973342532Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:26:19.682200535Z"}, {"textPayload": "[dd.trace 2025-06-30 09:29:01:664 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "e45hybt1rnftdk17", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T15:29:01.665216336Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:29:19.377185257Z"}, {"textPayload": "[dd.trace 2025-06-30 09:31:18:890 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "8js9t28faixfdo93", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T15:31:18.890284534Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:31:23.849220016Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "uskyoppd00fjdm6m", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "location": "us-central1", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T15:31:18.890359444Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:31:23.849220016Z"}, {"textPayload": "[dd.trace 2025-06-30 09:34:08:389 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "h5as6oryqofq1rct", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T15:34:08.390155144Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:34:23.917811485Z"}, {"textPayload": "[dd.trace 2025-06-30 09:37:59:817 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = error reading from server: read tcp 127.0.0.1:59160->127.0.0.1:5001: read: connection reset by peer", "insertId": "7ykgtks9tvgw8no2", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1"}}, "timestamp": "2025-06-30T15:37:59.817501949Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:38:04.256887533Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "ujm39kftqd5y0it9", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T15:37:59.817519179Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:38:04.256887533Z"}, {"textPayload": "[dd.trace 2025-06-30 09:39:08:392 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "qfolr7o9wnkiwdq9", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T15:39:08.392970116Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:39:23.924012967Z"}, {"textPayload": "[dd.trace 2025-06-30 09:43:03:797 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "083pyabnfn9mqztm", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T15:43:03.797524729Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:43:08.756177212Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "hiuq38c5mnvffr04", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T15:43:03.797572459Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:43:08.756177212Z"}, {"textPayload": "[dd.trace 2025-06-30 09:44:09:768 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "r0590yvffrnyzwr4", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T15:44:09.771989879Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:44:29.867486367Z"}, {"textPayload": "[dd.trace 2025-06-30 09:48:03:872 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "nr3h58gu894wi018", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T15:48:03.872701932Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:48:08.754436678Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "0utebx0j9otdslci", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T15:48:03.872755792Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:48:08.754436678Z"}, {"textPayload": "[dd.trace 2025-06-30 09:49:15:567 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "5ucsx51dssnzfq13", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T15:49:15.568139364Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:49:34.383478389Z"}, {"textPayload": "[dd.trace 2025-06-30 09:54:31:663 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "ukd9irjhmttk78fm", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T15:54:31.664234340Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:54:38.843656618Z"}, {"textPayload": "[dd.trace 2025-06-30 09:54:33:809 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "utp5tf5g7jjyqxjl", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T15:54:33.809724669Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:54:38.843656618Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "3rgijgp9fosooowq", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T15:54:33.810329139Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:54:38.843656618Z"}, {"textPayload": "[dd.trace 2025-06-30 09:59:33:970 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "jquqi6mu98imy4u2", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T15:59:33.970936628Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:59:39.414470883Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "p0sfe05isrxnc922", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T15:59:33.970950758Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T15:59:39.414470883Z"}, {"textPayload": "[dd.trace 2025-06-30 10:00:39:672 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "5k6zzmr20qx4zyve", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T16:00:39.674973325Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:00:59.872059922Z"}, {"textPayload": "[dd.trace 2025-06-30 10:04:38:897 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "w105s68fxpuzpjfe", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1"}}, "timestamp": "2025-06-30T16:04:38.897747755Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:04:44.180996586Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "22qnra86iyvywj42", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T16:04:38.897761445Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:04:44.180996586Z"}, {"textPayload": "[dd.trace 2025-06-30 10:05:45:564 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "e8x5utkaaxbqqu6l", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T16:05:45.565231652Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:06:04.373233576Z"}, {"textPayload": "[dd.trace 2025-06-30 10:10:53:688 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "sa9gw5pj70c1mdvp", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:10:53.691185857Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:11:08.926890729Z"}, {"textPayload": "[dd.trace 2025-06-30 10:12:39:087 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = error reading from server: read tcp 127.0.0.1:48608->127.0.0.1:5001: read: connection reset by peer", "insertId": "l0kfzuluhpcwp0yn", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T16:12:39.090311883Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:12:44.266265164Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "2mbyawn2jctb387w", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:12:39.091071093Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:12:44.266265164Z"}, {"textPayload": "[dd.trace 2025-06-30 10:15:57:566 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "eaeyljlmahjvzkf3", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:15:57.566664177Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:16:14.869478675Z"}, {"textPayload": "[dd.trace 2025-06-30 10:17:43:917 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "p989ttqk1zs7fgna", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:17:43.917756068Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:17:48.768151541Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "jg1mfgefmeuh1kv3", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T16:17:43.917804768Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:17:48.768151541Z"}, {"textPayload": "[dd.trace 2025-06-30 10:20:57:568 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "gihur5j29r36kpl0", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:20:57.568300072Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:21:14.872243863Z"}, {"textPayload": "[dd.trace 2025-06-30 10:22:48:875 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "a8kzs4p73bnxcogo", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:22:48.876181026Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:22:53.761735996Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "d54jg4vspw03tyzg", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T16:22:48.876200826Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:22:53.761735996Z"}, {"textPayload": "[dd.trace 2025-06-30 10:27:27:664 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "2btcq77b0m14z4gq", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T16:27:27.665250413Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:27:44.873099244Z"}, {"textPayload": "[dd.trace 2025-06-30 10:29:03:805 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "zl2qhxek4x9qlunx", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:29:03.805563978Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:29:08.833898166Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "0t1p22o8ygvk29tn", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:29:03.805627098Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:29:08.833898166Z"}, {"textPayload": "[dd.trace 2025-06-30 10:32:31:563 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "qfujbxwhzwryesk4", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1"}}, "timestamp": "2025-06-30T16:32:31.564174732Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:32:49.363153682Z"}, {"textPayload": "[dd.trace 2025-06-30 10:34:03:956 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "34rklqk3leiw5v6h", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:34:03.958107888Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:34:08.976340520Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "r9l9em4r6ok3m7tp", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:34:03.958155568Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:34:08.976340520Z"}, {"textPayload": "[dd.trace 2025-06-30 10:37:31:567 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "iyglep4miapu9ee3", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1"}}, "timestamp": "2025-06-30T16:37:31.567757896Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:37:49.382239779Z"}, {"textPayload": "[dd.trace 2025-06-30 10:39:08:851 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "8f9c2e04eu03i95w", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:39:08.851565059Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:39:14.160443203Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "vke3e5fdgorbi51j", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "pod_name": "ms-pld-6fdbcbcb86-nm9hx"}}, "timestamp": "2025-06-30T16:39:08.851609059Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:39:14.160443203Z"}, {"textPayload": "[dd.trace 2025-06-30 10:46:53:679 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "bhw0ndpg3hyvbndc", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:46:53.680283340Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:47:08.939365914Z"}, {"textPayload": "[dd.trace 2025-06-30 10:47:55:777 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = error reading from server: read tcp 127.0.0.1:41790->127.0.0.1:5001: read: connection reset by peer", "insertId": "5klow4b9jx77lwoo", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:47:55.777620303Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:47:58.937038257Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "jc776mrdki52vgp8", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:47:55.777644593Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:47:58.937038257Z"}, {"textPayload": "Picked up JAVA_TOOL_OPTIONS: -javaagent:/opt/datadog/apm/library/java/dd-java-agent.jar -XX:+IgnoreUnrecognizedVMOptions -XX:OnError=\"/tmp/datadog/java/dd_crash_uploader.sh %p\" -XX:ErrorFile=/tmp/datadog/java/hs_err_pid_%p.log -XX:OnOutOfMemoryError=\"/tmp/datadog/java/dd_oome_notifier.sh %p\"", "insertId": "r1nkfam7rglt5rez", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:05.289589908Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:51:11.386142413Z"}, {"textPayload": "OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended", "insertId": "txmaun1252iw3r25", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:05.854407745Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:51:11.386142413Z"}, {"textPayload": "[dd.trace 2025-06-30 10:51:15:584 -0600] [dd-task-scheduler] INFO datadog.trace.agent.core.StatusLogger - DATADOG TRACER CONFIGURATION {\"version\":\"1.50.1~90a4810101\",\"os_name\":\"Linux\",\"os_version\":\"6.6.72+\",\"architecture\":\"amd64\",\"lang\":\"jvm\",\"lang_version\":\"17.0.15\",\"jvm_vendor\":\"Eclipse Adoptium\",\"jvm_version\":\"17.0.15+6\",\"java_class_version\":\"61.0\",\"http_nonProxyHosts\":\"null\",\"http_proxyHost\":\"null\",\"enabled\":true,\"service\":\"ms-pld\",\"agent_url\":\"unix:///var/run/datadog/apm.socket\",\"agent_unix_domain_socket\":\"/var/run/datadog/apm.socket\",\"agent_error\":false,\"debug\":false,\"trace_propagation_style_extract\":[\"datadog\",\"tracecontext\",\"baggage\"],\"trace_propagation_style_inject\":[\"datadog\",\"tracecontext\",\"baggage\"],\"analytics_enabled\":false,\"priority_sampling_enabled\":true,\"logs_correlation_enabled\":true,\"profiling_enabled\":false,\"remote_config_enabled\":true,\"debugger_enabled\":false,\"debugger_exception_enabled\":false,\"debugger_span_origin_enabled\":false,\"debugger_distributed_debugger_enabled\":false,\"appsec_enabled\":\"ENABLED_INACTIVE\",\"rasp_enabled\":true,\"telemetry_enabled\":true,\"telemetry_dependency_collection_enabled\":true,\"telemetry_log_collection_enabled\":true,\"dd_version\":\"20250630.4-dev\",\"health_checks_enabled\":true,\"configuration_file\":\"no config file present\",\"runtime_id\":\"500add48-e403-4add-a48f-a3fdcbc95e51\",\"logging_settings\":{\"showDateTime\":true,\"showLogName\":true,\"showThreadName\":true,\"defaultLogLevel\":\"INFO\",\"embedException\":false,\"levelInBrackets\":false,\"dateTimeFormat\":\"'[dd.trace 'yyyy-MM-dd HH:mm:ss:SSS Z']'\",\"logFile\":\"System.err\",\"configurationFile\":\"simplelogger.properties\",\"showShortLogName\":false,\"jsonEnabled\":false,\"warnLevelString\":\"WARN\"},\"cws_enabled\":false,\"cws_tls_refresh\":5000,\"datadog_profiler_enabled\":false,\"datadog_profiler_safe\":true,\"datadog_profiler_enabled_overridden\":false,\"data_streams_enabled\":false}", "insertId": "vx41ij0bi2tgbxlz", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:15.716441156Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:51:16.223321653Z"}, {"textPayload": "[dd.trace 2025-06-30 10:51:16:849 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "fm7f0y784x5t8do8", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:16.853413738Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:51:21.594704823Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "uni0c7ab7vtppe7c", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:16.853440748Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:51:21.594704823Z"}, {"textPayload": "10:51:23,050 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.5.18", "insertId": "t3whx1wcy1u2avs3", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.312915491Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,096 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - Here is a list of configurators discovered as a service, by rank: ", "insertId": "8dde42606kldrsip", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:27.312966980Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,147 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 -   org.springframework.boot.logging.logback.RootLogLevelConfigurator", "insertId": "xlmbfnr4fxwh459u", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.312974860Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,147 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - They will be invoked in order until ExecutionStatus.DO_NOT_INVOKE_NEXT_IF_ANY is returned.", "insertId": "xcirs1jxzbpoue0u", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.312981031Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,147 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - Constructed configurator of type class org.springframework.boot.logging.logback.RootLogLevelConfigurator", "insertId": "y46g3m2hhsiqifcl", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.312989080Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,263 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - org.springframework.boot.logging.logback.RootLogLevelConfigurator.configure() call lasted 5 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY", "insertId": "zsfr4cc88flqk6m2", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.312996080Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,263 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator", "insertId": "5ue3sfbgbqxny0f0", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:27.313017331Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,282 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator", "insertId": "7ygu0i61la8r70g7", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313020751Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,288 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]", "insertId": "k5oitb57bfj366pv", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.313024820Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,289 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]", "insertId": "n30r9z6e5vwahou3", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:27.313028431Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,289 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 7 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY", "insertId": "7faafkhns44vxt7f", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313031940Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,289 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator", "insertId": "n0t3whg4zy19an2z", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313035860Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,351 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator", "insertId": "6kls56n8r9yaxq5t", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:27.313039711Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,381 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.xml]", "insertId": "9bdyzko6wyk6754k", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.313042900Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,382 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback.xml] at [jar:nested:/sysx/progs/app.jar/!BOOT-INF/classes/!/logback.xml]", "insertId": "u8qwmzfkborchea5", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:27.313046431Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:23,685 |-INFO in ConfigurationWatchList(mainURL=jar:nested:/sysx/progs/app.jar/!BOOT-INF/classes/!/logback.xml, fileWatchList={}, urlWatchList=[}) - URL [jar:nested:/sysx/progs/app.jar/!BOOT-INF/classes/!/logback.xml] is not of type file", "insertId": "c4hjpor5cqvwdqbi", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:27.313050080Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:25,751 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [<PERSON><PERSON><PERSON>]", "insertId": "92ispezck3z5ytsr", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313053351Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:25,751 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]", "insertId": "2hozx5s4yf3kzz4h", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313056460Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,590 |-WAR<PERSON> in ch.qos.logback.core.ConsoleAppender[Console] - This appender no longer admits a layout as a sub-component, set an encoder instead.", "insertId": "lyz3lm7dky65x4tn", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:27.313059611Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,590 |-WARN in ch.qos.logback.core.ConsoleAppender[Console] - To ensure compatibility, wrapping your layout in LayoutWrappingEncoder.", "insertId": "tfcqn7ekwhq1xu9d", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "location": "us-central1", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:27.313063871Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,590 |-WARN in ch.qos.logback.core.ConsoleAppender[Console] - See also https://logback.qos.ch/codes.html#layoutInsteadOfEncoder for details", "insertId": "o5vqzfsfc0ho0lkr", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:27.313067351Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,590 |-INFO in ch.qos.logback.core.ConsoleAppender[Console] - BEWARE: Writing to the console can be very slow. Avoid logging to the ", "insertId": "jjjgic9kz1oapoaq", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313070680Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,590 |-INFO in ch.qos.logback.core.ConsoleAppender[Console] - console in production environments, especially in high volume systems.", "insertId": "lus16avlruqgu1ja", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.313073951Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,590 |-INFO in ch.qos.logback.core.ConsoleAppender[Console] - See also https://logback.qos.ch/codes.html#slowConsole", "insertId": "s04l7v5z35u79mae", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.313077380Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,590 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [RollingFile]", "insertId": "ek8ipchle2t79a66", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.313080811Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,590 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]", "insertId": "hma8rg619syeh5io", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313084040Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,682 |-INFO in ch.qos.logback.core.model.processor.ModelInterpretationContext@62a68bcb - value \"log-path_IS_UNDEFINED/filename_IS_UNDEFINED.log\" substituted for \"${log-path}/${filename}.log\"", "insertId": "q49saj0hkmsthalk", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.313098091Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,687 |-INFO in ch.qos.logback.core.model.processor.ModelInterpretationContext@62a68bcb - value \"log-path_IS_UNDEFINED/archived/filename_IS_UNDEFINED-%d{yyyy-MM-dd}.%i.log\" substituted for \"${log-path}/archived/${filename}-%d{yyyy-MM-dd}.%i.log\"", "insertId": "tl8gw1jx8kdevg9o", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:27.313102Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,692 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@25916650 - No compression will be used", "insertId": "bticrdbw52eqlthr", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.313111971Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,766 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@25916650 - Will use the pattern log-path_IS_UNDEFINED/archived/filename_IS_UNDEFINED-%d{yyyy-MM-dd}.%i.log for the active file", "insertId": "y7z9s182lvc0krol", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.313115700Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:26,766 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@14924f41 - SizeAndTimeBasedFNATP class was renamed as SizeAndTimeBasedFileNamingAndTriggeringPolicy.", "insertId": "8h9j25vy4xo4gxtw", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313119Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,051 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@14924f41 - The date pattern is 'yyyy-MM-dd' from file name pattern 'log-path_IS_UNDEFINED/archived/filename_IS_UNDEFINED-%d{yyyy-MM-dd}.%i.log'.", "insertId": "jbrd1ycb6l1tb4p7", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:27.313122291Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,051 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@14924f41 - Roll-over at midnight.", "insertId": "6zdxdh2zes6enwb9", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:27.313125951Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,118 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@14924f41 - Setting initial period to 2025-06-30T16:51:27.117Z", "insertId": "7ppftaa0iu6ds4z5", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:27.313129011Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,118 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@14924f41 - Direct use of either SizeAndTimeBasedFNATP or SizeAndTimeBasedFileNamingAndTriggeringPolicy ", "insertId": "7wn91qowvi9yycgc", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313131840Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,118 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@14924f41 - is deprecated. Please use SizeAndTimeBasedRollingPolicy instead.", "insertId": "dr7w5bcbu4kjruz0", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.313134951Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,118 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@14924f41 - For more information see https://logback.qos.ch/manual/appenders.html#SizeAndTimeBasedRollingPolicy", "insertId": "4yb7xt04pn904mi1", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313138240Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,191 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[RollingFile] - Active log file name: log-path_IS_UNDEFINED/filename_IS_UNDEFINED.log", "insertId": "zlzo0xnwm6lfmvfk", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:27.313141671Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,195 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[RollingFile] - File property is set to [log-path_IS_UNDEFINED/filename_IS_UNDEFINED.log]", "insertId": "l567okvdczt5vnxo", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:27.313144811Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,199 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO", "insertId": "uoyntjhswjynzjx7", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.313147980Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,206 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [RollingFile] to Logger[ROOT]", "insertId": "l5iaz46bm5d52ome", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:27.313150911Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,250 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [<PERSON>sole] to <PERSON>gger[ROOT]", "insertId": "hm16aezm3uxtsfuv", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:27.313153960Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,254 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [com.coppel] to TRACE", "insertId": "0tt40jouzy8nkjx8", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:27.313156960Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,265 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [com.coppel] to false", "insertId": "7bmkvcizmvjjp93w", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:27.313160151Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,265 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [RollingFile] to Logger[com.coppel]", "insertId": "kde7lynltwd2lgnm", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:27.313163191Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,265 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [<PERSON>sol<PERSON>] to Logger[com.coppel]", "insertId": "txt80n53w30bxrq7", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.313166751Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,265 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@493da830 - End of configuration.", "insertId": "3dgjr3f9nbp8wgsh", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.313170180Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,288 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@1f939a0f - Registering current configuration as safe fallback point", "insertId": "uyzooe9qjryci0qq", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:27.313173651Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "10:51:27,288 |-INFO in ch.qos.logback.classic.util.ContextInitializer@40247d48 - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 3937 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY", "insertId": "2kcw48ramhm5top3", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:27.313177191Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"insertId": "e5s04jcso57nrg18", "jsonPayload": {}, "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:27.313189160Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:31.423609005Z"}, {"textPayload": "[dd.trace 2025-06-30 10:51:28:748 -0600] [dd-task-scheduler] INFO datadog.communication.monitor.DDAgentStatsDConnection - Detected /var/run/datadog/dsd.socket.  Using it to send StatsD data.", "insertId": "0jnlbm8rfnbat44z", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:28.748803783Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:51:31.468943133Z"}, {"textPayload": "10:51:36,657 |-INFO in ConfigurationWatchList(mainURL=jar:nested:/sysx/progs/app.jar/!BOOT-INF/classes/!/logback.xml, fileWatchList={}, urlWatchList=[}) - URL [jar:nested:/sysx/progs/app.jar/!BOOT-INF/classes/!/logback.xml] is not of type file", "insertId": "ue3q347780f08ar0", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:37.012287698Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,780 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [<PERSON><PERSON><PERSON>]", "insertId": "2c60bzj41plta378", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024554347Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,780 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]", "insertId": "g8c00fftoo112uvu", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024573216Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,793 |-WAR<PERSON> in ch.qos.logback.core.ConsoleAppender[Console] - This appender no longer admits a layout as a sub-component, set an encoder instead.", "insertId": "1mwysm4nwpu65qdu", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.024578676Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,793 |-WARN in ch.qos.logback.core.ConsoleAppender[Console] - To ensure compatibility, wrapping your layout in LayoutWrappingEncoder.", "insertId": "7hx4p01ocxyaqqva", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:37.024585396Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,793 |-WARN in ch.qos.logback.core.ConsoleAppender[Console] - See also https://logback.qos.ch/codes.html#layoutInsteadOfEncoder for details", "insertId": "rclk7f829xx9vpar", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:37.024590156Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,793 |-INFO in ch.qos.logback.core.ConsoleAppender[Console] - BEWARE: Writing to the console can be very slow. Avoid logging to the ", "insertId": "g53zfptl67avxsdb", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.024594887Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,793 |-INFO in ch.qos.logback.core.ConsoleAppender[Console] - console in production environments, especially in high volume systems.", "insertId": "ykal3njwraxznkpr", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024600296Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,793 |-INFO in ch.qos.logback.core.ConsoleAppender[Console] - See also https://logback.qos.ch/codes.html#slowConsole", "insertId": "xzyi33cuy8j1bxj3", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024605367Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,796 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [RollingFile]", "insertId": "bpcb89vffmiq6w3x", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024610147Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,796 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]", "insertId": "kx26hv5n5bnvhk4t", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:37.024616196Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,797 |-INFO in ch.qos.logback.core.model.processor.ModelInterpretationContext@3820cfe - value \"log-path_IS_UNDEFINED/filename_IS_UNDEFINED.log\" substituted for \"${log-path}/${filename}.log\"", "insertId": "9b1xyunmcwojgbs0", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:37.024621847Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,839 |-INFO in ch.qos.logback.core.model.processor.ModelInterpretationContext@3820cfe - value \"log-path_IS_UNDEFINED/archived/filename_IS_UNDEFINED-%d{yyyy-MM-dd}.%i.log\" substituted for \"${log-path}/archived/${filename}-%d{yyyy-MM-dd}.%i.log\"", "insertId": "irdgds03tphhx68x", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:37.024626887Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,856 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@604480364 - No compression will be used", "insertId": "f16rznugf909ww9c", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:37.024631747Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,856 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@604480364 - Will use the pattern log-path_IS_UNDEFINED/archived/filename_IS_UNDEFINED-%d{yyyy-MM-dd}.%i.log for the active file", "insertId": "8lmtlahj6gm400m2", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.024637847Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,856 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5ec9eefa - SizeAndTimeBasedFNATP class was renamed as SizeAndTimeBasedFileNamingAndTriggeringPolicy.", "insertId": "i14a6lei0hjd7n8j", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:37.024642756Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,874 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5ec9eefa - The date pattern is 'yyyy-MM-dd' from file name pattern 'log-path_IS_UNDEFINED/archived/filename_IS_UNDEFINED-%d{yyyy-MM-dd}.%i.log'.", "insertId": "r21g8d2yhjl70owt", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.024647787Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,874 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5ec9eefa - Roll-over at midnight.", "insertId": "alhzkr6f1ik6415v", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024652567Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,924 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5ec9eefa - Setting initial period to 2025-06-30T16:51:27.198Z", "insertId": "9qryqzpepopebqj7", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024657316Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,924 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5ec9eefa - Direct use of either SizeAndTimeBasedFNATP or SizeAndTimeBasedFileNamingAndTriggeringPolicy ", "insertId": "fpzi1hgbhi3tpu2a", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:37.024674167Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,924 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5ec9eefa - is deprecated. Please use SizeAndTimeBasedRollingPolicy instead.", "insertId": "t4e03f2q688a0b86", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:37.024677516Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,924 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5ec9eefa - For more information see https://logback.qos.ch/manual/appenders.html#SizeAndTimeBasedRollingPolicy", "insertId": "m7omc632wx2ltbf4", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:37.024680496Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,925 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[RollingFile] - Active log file name: log-path_IS_UNDEFINED/filename_IS_UNDEFINED.log", "insertId": "ckh098pf9gote5f5", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024683996Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,957 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[RollingFile] - Setting currentFileLength to 0 for log-path_IS_UNDEFINED/filename_IS_UNDEFINED.log", "insertId": "e9sdg379lcua8eq4", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.024687627Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,957 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[RollingFile] - File property is set to [log-path_IS_UNDEFINED/filename_IS_UNDEFINED.log]", "insertId": "v94zsdncl0z8uy6f", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:37.024690867Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,957 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO", "insertId": "7otzdgg9ck4pmy3c", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:37.024724467Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,963 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@28b8f98a - Propagating INFO level on Logger[ROOT] onto the JUL framework", "insertId": "ihbm25e6fi2wx7ne", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:37.024729376Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,968 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [RollingFile] to Logger[ROOT]", "insertId": "pibfjvjetkf7nibt", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:37.024732587Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,968 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [Console] to <PERSON><PERSON>[ROOT]", "insertId": "odas979legzc7tbt", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.024735996Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,968 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [com.coppel] to TRACE", "insertId": "lsnnab6zufvvhuda", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:37.024739116Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,969 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@28b8f98a - Propagating TRACE level on Logger[com.coppel] onto the JUL framework", "insertId": "fzsbnxijz2cxfqkh", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:37.024742476Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,969 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [com.coppel] to false", "insertId": "u4sozblpzx0nxdfu", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:37.024745676Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,971 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [RollingFile] to Logger[com.coppel]", "insertId": "n1q91eo9dxgf7tfu", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:37.024748976Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,971 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [<PERSON>sol<PERSON>] to <PERSON>gger[com.coppel]", "insertId": "yas8mdwhspqt8l99", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:37.024752127Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,971 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@3b4ef59f - End of configuration.", "insertId": "gquywxafm5ty91ax", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.024755327Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "10:51:36,972 |-INFO in org.springframework.boot.logging.logback.SpringBootJoranConfigurator@22cb3d59 - Registering current configuration as safe fallback point", "insertId": "nce9b1cctal1vuas", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.024758707Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"insertId": "p3x4wlc6k1nlr8bf", "jsonPayload": {}, "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:37.025385126Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"insertId": "xgz9rau5tapo62sh", "jsonPayload": {}, "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.158095634Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "  .   ____          _            __ _ _", "insertId": "329ql6lo1nxg70yd", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.159212534Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": " /\\\\ / ___'_ __ _ _(_)_ __  __ _ \\ \\ \\ \\", "insertId": "7nvel70ojcv98f2h", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.159228854Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "( ( )\\___ | '_ | '_| | '_ \\/ _` | \\ \\ \\ \\", "insertId": "73zrs6yvpl70xo9d", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:37.159233404Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": " \\\\/  ___)| |_)| | | | | || (_| |  ) ) ) )", "insertId": "g4fqerpz911nqxg3", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:37.159237024Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "  '  |____| .__|_| |_|_| |_\\__, | / / / /", "insertId": "z9osaymqr2fbntg8", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.159240234Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": " =========|_|==============|___/=/_/_/_/", "insertId": "2zskfc8wnjnvmt5y", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:37.159243424Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"insertId": "6jbx25smq1dmbk2z", "jsonPayload": {}, "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.159246324Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": " :: Spring Boot ::               (v3.3.11)", "insertId": "yc2j2bsd0nnh0hnu", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:37.161189694Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"insertId": "awppe301avoea7ov", "jsonPayload": {}, "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:37.161410304Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "\u001b[30m2025-06-30 10:51:39,384\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.StartupInfoLogger\u001b[0;39m: Starting MainApplication v1.1.0 using Java 17.0.15 with PID 1 (/sysx/progs/app.jar started by root in /sysx/progs)", "insertId": "6x0qb8i3bnz9k8ta", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "location": "us-central1", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:39.447896316Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "\u001b[30m2025-06-30 10:51:39,449\u001b[0;39m \u001b[39mDEBUG\u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.<PERSON>upInfoLogger\u001b[0;39m: Running with Spring Boot v3.3.11, Spring v6.2.7", "insertId": "5210qvog7szne7xu", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1"}}, "timestamp": "2025-06-30T16:51:39.449731146Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "\u001b[30m2025-06-30 10:51:39,452\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.SpringApplication\u001b[0;39m: No active profile set, falling back to 1 default profile: \"default\"", "insertId": "wu4uue77b863w6k3", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:51:39.453040186Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:41.259970309Z"}, {"textPayload": "[dd.trace 2025-06-30 10:51:44:280 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "76b38q78dio2kb9g", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:44.281861298Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:52:02.620562408Z"}, {"textPayload": "\u001b[30m2025-06-30 10:51:54,193\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.d.r.c.RepositoryConfigurationDelegate\u001b[0;39m: Bootstrapping Spring Data JPA repositories in DEFAULT mode.", "insertId": "60nubbdw11cwguvc", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:51:54.193936612Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:56.245713423Z"}, {"textPayload": "\u001b[30m2025-06-30 10:51:54,394\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.d.r.c.RepositoryConfigurationDelegate\u001b[0;39m: Finished Spring Data repository scanning in 104 ms. Found 0 JPA repository interfaces.", "insertId": "t6hm48n8qojhou02", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "location": "us-central1", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:51:54.395749058Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:51:56.245713423Z"}, {"textPayload": "\u001b[30m2025-06-30 10:51:56,824\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.c.c.s.GenericScope\u001b[0;39m: BeanFactory id=68fab8de-7d7b-3f42-b376-476a87665236", "insertId": "icqu2p7794p7kylh", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:51:56.825157157Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:01.579667149Z"}, {"textPayload": "[dd.trace 2025-06-30 10:51:57:563 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "l4fvyzirpdev4kld", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:51:57.563506046Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:52:14.864622203Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:05,287\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.w.e.t.TomcatWebServer\u001b[0;39m: Tomcat initialized with port 8080 (http)", "insertId": "0oj5z23flqp47p1m", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:52:05.288147319Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:06.271442878Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:05,645\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.a.j.l.DirectJDKLog\u001b[0;39m: Initializing ProtocolHandler [\"http-nio-8080\"]", "insertId": "gatuo099lk5csd3o", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:52:05.647334580Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:06.271442878Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:05,688\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.a.j.l.DirectJDKLog\u001b[0;39m: Starting service [Tomcat]", "insertId": "02c2q81lj5ax<PERSON>ou", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:52:05.690552993Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:06.271442878Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:05,779\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.a.j.l.DirectJDKLog\u001b[0;39m: Starting Servlet engine: [Apache Tomcat/10.1.42]", "insertId": "9wejcfag1blkxqla", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:52:05.783465437Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:06.271442878Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:07,188\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.a.j.l.DirectJDKLog\u001b[0;39m: Initializing Spring embedded WebApplicationContext", "insertId": "skl57pu6xmvnv26w", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:52:07.190950995Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:11.334619581Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:07,246\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.w.s.c.ServletWebServerApplicationContext\u001b[0;39m: Root WebApplicationContext: initialization completed in 27260 ms", "insertId": "n8y6qd0ivuo1e9t8", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:52:07.247233665Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:11.334619581Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:13,850\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mc.z.h.HikariDataSource\u001b[0;39m:  - Starting...", "insertId": "lj7h4h9nzjojpnba", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:52:13.856268154Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:16.148096917Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:17,894\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mc.z.h.p.HikariPool\u001b[0;39m:  - Added connection ConnectionID:1 ClientConnectionId: e45bf96d-e7a0-428c-835e-162c55412ebb", "insertId": "vf8bgy6zi82bjksn", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1"}}, "timestamp": "2025-06-30T16:52:17.898413926Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:21.259463646Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:17,988\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mc.z.h.HikariDataSource\u001b[0;39m:  - Start completed.", "insertId": "rwinxfo54ggept7w", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:52:17.990846261Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:21.259463646Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:19,586\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.h.j.i.u.<PERSON>g<PERSON>elper\u001b[0;39m: HHH000204: Processing PersistenceUnitInfo [name: default]", "insertId": "ehrz1vr8sr314qji", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:52:19.587537627Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:21.259463646Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:20,409\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.h.Version\u001b[0;39m: HHH000412: Hibernate ORM core version 6.5.3.Final", "insertId": "6wqa63y50yu84scd", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:52:20.447965625Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:21.259463646Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:20,885\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.h.c.i.RegionFactoryInitiator\u001b[0;39m: HHH000026: Second-level cache disabled", "insertId": "dglop3s7cjm6zhzv", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:52:20.887565853Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:21.259463646Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:24,352\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.o.j.p.SpringPersistenceUnitInfo\u001b[0;39m: No LoadTimeWeaver setup: ignoring JPA class transformer", "insertId": "q7x04y00tf4v9wkz", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:52:24.356048080Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:26.363180590Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:24,880\u001b[0;39m \u001b[31mWARN \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.h.e.j.d.i.DialectFactoryImpl\u001b[0;39m: HHH90000025: SQLServer2012Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)", "insertId": "66w4u3zgcf26n17j", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:52:24.881480573Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:26.363180590Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:24,883\u001b[0;39m \u001b[31mWARN \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.h.e.j.d.i.DialectFactoryImpl\u001b[0;39m: HHH90000026: SQLServer2012Dialect has been deprecated; use org.hibernate.dialect.SQLServerDialect instead", "insertId": "eabmcz5f8qg1dc8m", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:52:24.884223712Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:26.363180590Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:26,870\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.h.v.i.u.Version\u001b[0;39m: HV000001: Hibernate Validator 8.0.2.Final", "insertId": "536ta3unff4x11ds", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:52:26.871477534Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:31.421600578Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:31,244\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.h.e.t.j.p.i.JtaPlatformInitiator\u001b[0;39m: HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)", "insertId": "kdtgbtzn3204hosh", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1"}}, "timestamp": "2025-06-30T16:52:31.245325142Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:36.330324106Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:31,347\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.o.j.AbstractEntityManagerFactoryBean\u001b[0;39m: Initialized JPA EntityManagerFactory for persistence unit 'default'", "insertId": "qafonqf9vpyakk8x", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T16:52:31.349701715Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:36.330324106Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:35,861\u001b[0;39m \u001b[31mWARN \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration\u001b[0;39m: spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning", "insertId": "9x55oexqcxknmnba", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:52:35.883484546Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:36.330324106Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:46,089\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.a.h.H2ConsoleAutoConfiguration$H2ConsoleLogger\u001b[0;39m: H2 console available at '/h2-console'. Database available at '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************icationScheme=NativeAuthentication;xopenStates=false;datetimeParameterType=datetime2;sendTimeAsDatetime=true;replication=false;trustStoreType=JKS;trustServerCertificate=true;TransparentNetworkIPResolution=true;iPAddressPreference=IPv4First;serverNameAsACE=false;sendStringParametersAsUnicode=true;selectMethod=direct;responseBuffering=adaptive;queryTimeout=-1;packetSize=8000;multiSubnetFailover=false;loginTimeout=30;lockTimeout=-1;lastUpdateCount=true;useDefaultGSSCredential=false;prepareMethod=prepexec;encrypt=True;disableStatementPooling=true;databaseName=pld_coppel20160819;columnEncryptionSetting=Disabled;applicationName=Microsoft JDBC Driver for SQL Server;applicationIntent=readwrite;'", "insertId": "yw6087te58qy1mom", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:52:46.090444830Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:46.363788641Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:47,282\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.a.e.w.EndpointLinksResolver\u001b[0;39m: Exposing 1 endpoint beneath base path '/actuator'", "insertId": "zg3qqa5rtnaq598d", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:52:47.283621104Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:51.233791192Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:47,947\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.a.j.l.DirectJDKLog\u001b[0;39m: Starting ProtocolHandler [\"http-nio-8080\"]", "insertId": "l3hp8fcre5avbdvf", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:52:47.948054174Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:51.233791192Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:48,147\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.w.e.t.TomcatWebServer\u001b[0;39m: Tomcat started on port 8080 (http) with context path '/ms-pld/api/v1'", "insertId": "igmsdiq2awhuzm9n", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1"}}, "timestamp": "2025-06-30T16:52:48.148573911Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:51.233791192Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:48,286\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mmain\u001b[0;39m] \u001b[33mo.s.b.StartupInfoLogger\u001b[0;39m: Started MainApplication in 78.938 seconds (process running for 102.997)", "insertId": "1nur433qryd0tt9y", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:52:48.287464038Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:52:51.233791192Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:57,292\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mhttp-nio-8080-exec-2\u001b[0;39m] \u001b[33mo.a.j.l.DirectJDKLog\u001b[0;39m: Initializing Spring DispatcherServlet 'dispatcherServlet'", "insertId": "gmnnuvqark9z86us", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:52:57.292794401Z", "severity": "INFO", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:53:01.523765335Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:57,293\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mhttp-nio-8080-exec-2\u001b[0;39m] \u001b[33mo.s.w.s.FrameworkServlet\u001b[0;39m: Initializing Servlet 'dispatcherServlet'", "insertId": "drtgdzksgixek20n", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:52:57.293455161Z", "severity": "INFO", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:53:01.523765335Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:57,296\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mhttp-nio-8080-exec-2\u001b[0;39m] \u001b[33mo.s.w.s.FrameworkServlet\u001b[0;39m: Completed initialization in 3 ms", "insertId": "m4jvbeibx0g9dbmw", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T16:52:57.297409990Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:53:01.523765335Z"}, {"textPayload": "[dd.trace 2025-06-30 10:52:58:856 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "xqcrxsuw6gmrncrn", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "location": "us-central1"}}, "timestamp": "2025-06-30T16:52:58.856463064Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:53:03.813176753Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "5v1lejaut1xzq2n9", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "location": "us-central1"}}, "timestamp": "2025-06-30T16:52:58.856507174Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:53:03.813176753Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:59,306\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mSpringApplicationShutdownHook\u001b[0;39m] \u001b[33mo.s.o.j.AbstractEntityManagerFactoryBean\u001b[0;39m: Closing JPA EntityManagerFactory for persistence unit 'default'", "insertId": "sq8b8i49v64vn856", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:52:59.307927151Z", "severity": "INFO", "labels": {"k8s-pod/pod-template-hash": "6fdbcbcb86", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:53:03.757327956Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:59,309\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mSpringApplicationShutdownHook\u001b[0;39m] \u001b[33mc.z.h.HikariDataSource\u001b[0;39m:  - Shutdown initiated...", "insertId": "0795rfbl7226vy72", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1"}}, "timestamp": "2025-06-30T16:52:59.309745900Z", "severity": "INFO", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "6fdbcbcb86", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:53:03.757327956Z"}, {"textPayload": "\u001b[30m2025-06-30 10:52:59,312\u001b[0;39m \u001b[34mINFO \u001b[0;39m [\u001b[34mSpringApplicationShutdownHook\u001b[0;39m] \u001b[33mc.z.h.HikariDataSource\u001b[0;39m:  - Shutdown completed.", "insertId": "lag8tmatjfuwxpn4", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-6fdbcbcb86-nm9hx", "container_name": "ms-pld", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:52:59.313246259Z", "severity": "INFO", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-4346c801-gqrb", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "6fdbcbcb86"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stdout", "receiveTimestamp": "2025-06-30T16:53:03.757327956Z"}, {"textPayload": "[dd.trace 2025-06-30 10:56:21:082 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "k9dmsxkn2py31ow0", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T16:56:21.083270141Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:56:21.390204858Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "bfdagpzs6w9fsmcv", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T16:56:21.083324381Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:56:21.390204858Z"}, {"textPayload": "[dd.trace 2025-06-30 10:57:04:209 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "4lnuxf3v33l2ky7o", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T16:57:04.209771692Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T16:57:22.114213342Z"}, {"textPayload": "[dd.trace 2025-06-30 11:01:26:135 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "mad2zaomg1qhvjl2", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T17:01:26.135966409Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:01:31.138980217Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "6f08bhvg65av81s2", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T17:01:26.136015040Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:01:31.138980217Z"}, {"textPayload": "[dd.trace 2025-06-30 11:02:14:089 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "uurpakn121r9b76f", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T17:02:14.089920924Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:02:32.619602796Z"}, {"textPayload": "[dd.trace 2025-06-30 11:06:26:352 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: authentication handshake failed: read tcp 127.0.0.1:60506->127.0.0.1:5001: read: connection reset by peer\"", "insertId": "a847wleu8pw96fx2", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T17:06:26.352590558Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:06:31.264332555Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "1acb4xouox7kr2au", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "location": "us-central1", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T17:06:26.352882349Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:06:31.264332555Z"}, {"textPayload": "[dd.trace 2025-06-30 11:07:14:091 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "ylk9vhgq5mkj3x43", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T17:07:14.092030461Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:07:32.631891508Z"}, {"textPayload": "[dd.trace 2025-06-30 11:12:34:193 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "5ofdz8i761dk1iwy", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T17:12:34.193778867Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:12:46.242776990Z"}, {"textPayload": "[dd.trace 2025-06-30 11:12:46:088 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "wzooo2nuyt9giz7r", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T17:12:46.088417083Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:12:46.242776990Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "jvtb5lyj8yyknd11", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T17:12:46.088475873Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:12:46.242776990Z"}, {"textPayload": "[dd.trace 2025-06-30 11:17:44:084 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "qi1n5rfbgfkjqb3h", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "location": "us-central1", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T17:17:44.085169375Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:17:56.160069391Z"}, {"textPayload": "[dd.trace 2025-06-30 11:17:51:259 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "hansqkui9b21vhmy", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T17:17:51.259339662Z", "severity": "ERROR", "labels": {"compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:17:56.160069391Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "t8ubkk1i9bs9el6m", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T17:17:51.259409491Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:17:56.160069391Z"}, {"textPayload": "[dd.trace 2025-06-30 11:22:44:093 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "gva3enhyvz9ymmwo", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T17:22:44.094034538Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:22:56.375155725Z"}, {"textPayload": "[dd.trace 2025-06-30 11:22:51:464 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "ba9pliqv3o0g6b0p", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T17:22:51.464490802Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:22:56.375155725Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "28kn6falob0tcmaf", "resource": {"type": "k8s_container", "labels": {"container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T17:22:51.464857251Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:22:56.375155725Z"}, {"textPayload": "[dd.trace 2025-06-30 11:27:52:148 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = error reading from server: read tcp 127.0.0.1:51844->127.0.0.1:5001: read: connection reset by peer", "insertId": "j9fhdwov182bvm5i", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "container_name": "ms-pld", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T17:27:52.150009571Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:27:56.265722606Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "gu94haop4vbsz1bq", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T17:27:52.150029181Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:27:56.265722606Z"}, {"textPayload": "[dd.trace 2025-06-30 11:28:02:110 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "64fj93s50mlrikav", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "container_name": "ms-pld", "namespace_name": "juridicolegal2"}}, "timestamp": "2025-06-30T17:28:02.111155058Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/pod-template-hash": "66b4f895cf", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:28:22.114830899Z"}, {"textPayload": "[dd.trace 2025-06-30 11:32:56:039 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "9xvo4v6swhb0c877", "resource": {"type": "k8s_container", "labels": {"location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T17:32:56.040100218Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:32:56.330718977Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "5pk4xgje3p0nj9me", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "location": "us-central1", "container_name": "ms-pld", "cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs"}}, "timestamp": "2025-06-30T17:32:56.040163227Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:32:56.330718977Z"}, {"textPayload": "[dd.trace 2025-06-30 11:33:18:193 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Resource temporarily unavailable (Will not log warnings for 5 minutes)", "insertId": "ihrbcnuhwovdr9fk", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T17:33:18.193857320Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:33:37.118921532Z"}, {"textPayload": "[dd.trace 2025-06-30 11:37:56:147 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "grn9fc0yz75xj9cb", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T17:37:56.148273055Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:38:01.260773158Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "y461mn7kducibwd8", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "project_id": "cpl-corp-jursaj-dev-07052024", "container_name": "ms-pld", "location": "us-central1"}}, "timestamp": "2025-06-30T17:37:56.148308855Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:38:01.260773158Z"}, {"textPayload": "[dd.trace 2025-06-30 11:38:32:121 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "5wbikb1rfiq9hvsb", "resource": {"type": "k8s_container", "labels": {"cluster_name": "gke-corp-jursaj-dev-01", "location": "us-central1", "project_id": "cpl-corp-jursaj-dev-07052024", "pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld"}}, "timestamp": "2025-06-30T17:38:32.121687777Z", "severity": "ERROR", "labels": {"k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_name": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:38:52.129965274Z"}, {"textPayload": "[dd.trace 2025-06-30 11:43:21:048 -0600] [dd-remote-config] WARN datadog.remoteconfig.ConfigurationPoller - Failed to retrieve remote configuration: unexpected response code Internal Server Error 500 rpc error: code = Unavailable desc = connection error: desc = \"transport: Error while dialing: dial tcp 127.0.0.1:5001: connect: connection refused\"", "insertId": "zfudp57j49jobv88", "resource": {"type": "k8s_container", "labels": {"project_id": "cpl-corp-jursaj-dev-07052024", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T17:43:21.049002135Z", "severity": "ERROR", "labels": {"logging.gke.io/top_level_controller_type": "Deployment", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_name": "ms-pld", "k8s-pod/admission_datadoghq_com/enabled": "true", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:43:21.407216700Z"}, {"textPayload": " (Will not log warnings for 5 minutes)", "insertId": "sumenhjolpapc7dy", "resource": {"type": "k8s_container", "labels": {"pod_name": "ms-pld-66b4f895cf-8kghs", "namespace_name": "juridicolegal2", "container_name": "ms-pld", "project_id": "cpl-corp-jursaj-dev-07052024", "location": "us-central1", "cluster_name": "gke-corp-jursaj-dev-01"}}, "timestamp": "2025-06-30T17:43:21.049110205Z", "severity": "ERROR", "labels": {"k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld", "k8s-pod/pod-template-hash": "66b4f895cf"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:43:21.407216700Z"}, {"textPayload": "[dd.trace 2025-06-30 11:43:32:152 -0600] [StatsD-Sender-1] WARN datadog.communication.monitor.DDAgentStatsDConnection - IOException in StatsD client - /var/run/datadog/dsd.socket java.io.IOException: Connection refused (Will not log warnings for 5 minutes)", "insertId": "l5qcpp1jnndqd806", "resource": {"type": "k8s_container", "labels": {"namespace_name": "juridicolegal2", "cluster_name": "gke-corp-jursaj-dev-01", "container_name": "ms-pld", "location": "us-central1", "pod_name": "ms-pld-66b4f895cf-8kghs", "project_id": "cpl-corp-jursaj-dev-07052024"}}, "timestamp": "2025-06-30T17:43:32.153245973Z", "severity": "ERROR", "labels": {"k8s-pod/pod-template-hash": "66b4f895cf", "logging.gke.io/top_level_controller_type": "Deployment", "k8s-pod/admission_datadoghq_com/enabled": "true", "logging.gke.io/top_level_controller_name": "ms-pld", "compute.googleapis.com/resource_name": "gke-gke-corp-jursaj--pool-corp-jursaj-57cd34b0-xh0b", "k8s-pod/workload_user_cattle_io/workloadselector": "ms-pld"}, "logName": "projects/cpl-corp-jursaj-dev-07052024/logs/stderr", "receiveTimestamp": "2025-06-30T17:43:52.116590076Z"}]