[INFO ] 2025-04-28 11:01:41.851 [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.1.Final
[INFO ] 2025-04-28 11:01:41.879 [main] c.c.MainApplication - Starting MainApplication using Java 17.0.14 with PID 61614 (/Users/<USER>/Downloads/ms-pld/target/classes started by gamansolutions in /Users/<USER>/Downloads/ms-pld)
[DEBUG] 2025-04-28 11:01:41.879 [main] c.c.MainApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
[INFO ] 2025-04-28 11:01:41.879 [main] c.c.MainApplication - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-04-28 11:01:42.313 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[INFO ] 2025-04-28 11:01:42.327 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
[INFO ] 2025-04-28 11:01:42.422 [main] o.s.c.c.s.GenericScope - BeanFactory id=51d8e9fd-94d9-3640-9dc9-89bf80058f5c
[INFO ] 2025-04-28 11:01:42.699 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 8080 (http)
[INFO ] 2025-04-28 11:01:42.705 [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-04-28 11:01:42.706 [main] o.a.c.c.StandardService - Starting service [Tomcat]
[INFO ] 2025-04-28 11:01:42.706 [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.26]
[INFO ] 2025-04-28 11:01:42.740 [main] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring embedded WebApplicationContext
[INFO ] 2025-04-28 11:01:42.740 [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 841 ms
[INFO ] 2025-04-28 11:01:42.835 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Starting...
[INFO ] 2025-04-28 11:01:42.985 [main] c.z.h.p.HikariPool - SpringBootJPAHikariCP - Added connection ConnectionID:1 ClientConnectionId: c02a9791-3eec-4ba6-9cee-83415ce01229
[INFO ] 2025-04-28 11:01:42.986 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Start completed.
[INFO ] 2025-04-28 11:01:42.992 [main] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at '**********************************************************************************************************************************kCopy=true;delayLoadingLobs=true;useFmtOnly=false;useBulkCopyForBatchInsert=false;cancelQueryTimeout=-1;sslProtocol=TLS;jaasConfigurationName=SQLJDBCDriver;statementPoolingCacheSize=0;serverPreparedStatementDiscardThreshold=10;enablePrepareOnFirstPreparedStatementCall=false;fips=false;socketTimeout=0;authentication=NotSpecified;authenticationScheme=nativeAuthentication;xopenStates=false;datetimeParameterType=datetime2;sendTimeAsDatetime=true;replication=false;trustStoreType=JKS;trustServerCertificate=true;TransparentNetworkIPResolution=true;iPAddressPreference=IPv4First;serverNameAsACE=false;sendStringParametersAsUnicode=true;selectMethod=direct;responseBuffering=adaptive;queryTimeout=-1;packetSize=8000;multiSubnetFailover=false;loginTimeout=30;lockTimeout=-1;lastUpdateCount=true;prepareMethod=prepexec;encrypt=true;disableStatementPooling=true;databaseName=master;columnEncryptionSetting=Disabled;applicationName=Microsoft JDBC Driver for SQL Server;applicationIntent=readwrite;'
[INFO ] 2025-04-28 11:01:43.089 [main] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
[INFO ] 2025-04-28 11:01:43.116 [main] o.h.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
[INFO ] 2025-04-28 11:01:43.131 [main] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
[INFO ] 2025-04-28 11:01:43.228 [main] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
[WARN ] 2025-04-28 11:01:43.247 [main] o.h.o.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[INFO ] 2025-04-28 11:01:43.396 [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[INFO ] 2025-04-28 11:01:43.398 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
[WARN ] 2025-04-28 11:01:43.475 [main] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[INFO ] 2025-04-28 11:01:43.859 [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
[INFO ] 2025-04-28 11:01:43.891 [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-04-28 11:01:43.898 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/ms-pld/api/v1'
[INFO ] 2025-04-28 11:01:43.910 [main] c.c.MainApplication - Started MainApplication in 2.207 seconds (process running for 2.454)
[INFO ] 2025-04-28 11:03:53.228 [http-nio-8080-exec-1] o.a.c.h.Http11Processor - Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x160x030x010x000xe50x010x000x000xe10x030x03bD0xf7}0xc6c?0xdb0xc2!0x170xa80xde0xe3-d ]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:407)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:262)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:904)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[INFO ] 2025-04-28 11:05:26.403 [http-nio-8080-exec-2] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO ] 2025-04-28 11:05:26.404 [http-nio-8080-exec-2] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
[INFO ] 2025-04-28 11:05:26.411 [http-nio-8080-exec-2] o.s.w.s.DispatcherServlet - Completed initialization in 7 ms
[INFO ] 2025-04-28 11:05:38.022 [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
[INFO ] 2025-04-28 11:05:38.025 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown initiated...
[INFO ] 2025-04-28 11:05:38.027 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown completed.
[INFO ] 2025-04-28 11:05:56.570 [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.1.Final
[INFO ] 2025-04-28 11:05:56.599 [main] c.c.MainApplication - Starting MainApplication using Java 17.0.14 with PID 61888 (/Users/<USER>/Downloads/ms-pld/target/classes started by gamansolutions in /Users/<USER>/Downloads/ms-pld)
[DEBUG] 2025-04-28 11:05:56.599 [main] c.c.MainApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
[INFO ] 2025-04-28 11:05:56.599 [main] c.c.MainApplication - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-04-28 11:05:57.046 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[INFO ] 2025-04-28 11:05:57.058 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
[INFO ] 2025-04-28 11:05:57.132 [main] o.s.c.c.s.GenericScope - BeanFactory id=51d8e9fd-94d9-3640-9dc9-89bf80058f5c
[INFO ] 2025-04-28 11:05:57.381 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 8080 (http)
[INFO ] 2025-04-28 11:05:57.386 [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-04-28 11:05:57.387 [main] o.a.c.c.StandardService - Starting service [Tomcat]
[INFO ] 2025-04-28 11:05:57.387 [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.26]
[INFO ] 2025-04-28 11:05:57.420 [main] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring embedded WebApplicationContext
[INFO ] 2025-04-28 11:05:57.420 [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 801 ms
[INFO ] 2025-04-28 11:05:57.521 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Starting...
[INFO ] 2025-04-28 11:05:57.661 [main] c.z.h.p.HikariPool - SpringBootJPAHikariCP - Added connection ConnectionID:1 ClientConnectionId: 048e679c-feb1-4315-b6c7-1c01f84958e0
[INFO ] 2025-04-28 11:05:57.662 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Start completed.
[INFO ] 2025-04-28 11:05:57.668 [main] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at '**********************************************************************************************************************************kCopy=true;delayLoadingLobs=true;useFmtOnly=false;useBulkCopyForBatchInsert=false;cancelQueryTimeout=-1;sslProtocol=TLS;jaasConfigurationName=SQLJDBCDriver;statementPoolingCacheSize=0;serverPreparedStatementDiscardThreshold=10;enablePrepareOnFirstPreparedStatementCall=false;fips=false;socketTimeout=0;authentication=NotSpecified;authenticationScheme=nativeAuthentication;xopenStates=false;datetimeParameterType=datetime2;sendTimeAsDatetime=true;replication=false;trustStoreType=JKS;trustServerCertificate=true;TransparentNetworkIPResolution=true;iPAddressPreference=IPv4First;serverNameAsACE=false;sendStringParametersAsUnicode=true;selectMethod=direct;responseBuffering=adaptive;queryTimeout=-1;packetSize=8000;multiSubnetFailover=false;loginTimeout=30;lockTimeout=-1;lastUpdateCount=true;prepareMethod=prepexec;encrypt=true;disableStatementPooling=true;databaseName=master;columnEncryptionSetting=Disabled;applicationName=Microsoft JDBC Driver for SQL Server;applicationIntent=readwrite;'
[INFO ] 2025-04-28 11:05:57.764 [main] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
[INFO ] 2025-04-28 11:05:57.790 [main] o.h.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
[INFO ] 2025-04-28 11:05:57.807 [main] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
[INFO ] 2025-04-28 11:05:57.909 [main] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
[WARN ] 2025-04-28 11:05:57.931 [main] o.h.o.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[INFO ] 2025-04-28 11:05:58.079 [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[INFO ] 2025-04-28 11:05:58.081 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
[WARN ] 2025-04-28 11:05:58.171 [main] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[INFO ] 2025-04-28 11:05:58.561 [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
[INFO ] 2025-04-28 11:05:58.592 [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-04-28 11:05:58.599 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/ms-pld/api/v1'
[INFO ] 2025-04-28 11:05:58.611 [main] c.c.MainApplication - Started MainApplication in 2.219 seconds (process running for 2.478)
[INFO ] 2025-04-28 11:06:10.895 [http-nio-8080-exec-1] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO ] 2025-04-28 11:06:10.895 [http-nio-8080-exec-1] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
[INFO ] 2025-04-28 11:06:10.897 [http-nio-8080-exec-1] o.s.w.s.DispatcherServlet - Completed initialization in 1 ms
[WARN ] 2025-04-28 11:06:11.027 [http-nio-8080-exec-1] c.c.e.AppExceptionHandler - [time: 2025-04-28T11:06:11.027480] [ip: 127.0.0.1] [user: sa] -- [ POST 500 ]
org.springframework.web.server.ResponseStatusException: 400 BAD_REQUEST "La función no está permitida para ejecución o no existe"
	at com.coppel.repositories.ProcesoNocturnoRepository.ejecutarFuncionPld(ProcesoNocturnoRepository.java:27)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:354)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:392)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.coppel.repositories.ProcesoNocturnoRepository$$SpringCGLIB$$0.ejecutarFuncionPld(<generated>)
	at com.coppel.services.impl.ProcesoNocturnoServiceImpl.ejecutarFuncionPld(ProcesoNocturnoServiceImpl.java:19)
	at com.coppel.controllers.ProcesosNocturnosController.ejecutarFuncion(ProcesosNocturnosController.java:27)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.coppel.filters.SessionFilter.doFilter(SessionFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:904)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[INFO ] 2025-04-28 11:08:49.838 [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
[INFO ] 2025-04-28 11:08:49.842 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown initiated...
[INFO ] 2025-04-28 11:08:49.845 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown completed.
[INFO ] 2025-04-28 11:08:57.000 [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.1.Final
[INFO ] 2025-04-28 11:08:57.027 [main] c.c.MainApplication - Starting MainApplication using Java 17.0.14 with PID 62050 (/Users/<USER>/Downloads/ms-pld/target/classes started by gamansolutions in /Users/<USER>/Downloads/ms-pld)
[DEBUG] 2025-04-28 11:08:57.028 [main] c.c.MainApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
[INFO ] 2025-04-28 11:08:57.028 [main] c.c.MainApplication - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-04-28 11:08:57.436 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[INFO ] 2025-04-28 11:08:57.449 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
[INFO ] 2025-04-28 11:08:57.521 [main] o.s.c.c.s.GenericScope - BeanFactory id=51d8e9fd-94d9-3640-9dc9-89bf80058f5c
[INFO ] 2025-04-28 11:08:57.755 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 8080 (http)
[INFO ] 2025-04-28 11:08:57.760 [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-04-28 11:08:57.761 [main] o.a.c.c.StandardService - Starting service [Tomcat]
[INFO ] 2025-04-28 11:08:57.761 [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.26]
[INFO ] 2025-04-28 11:08:57.793 [main] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring embedded WebApplicationContext
[INFO ] 2025-04-28 11:08:57.794 [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 748 ms
[INFO ] 2025-04-28 11:08:57.889 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Starting...
[INFO ] 2025-04-28 11:08:58.028 [main] c.z.h.p.HikariPool - SpringBootJPAHikariCP - Added connection ConnectionID:1 ClientConnectionId: 4f264340-b7b0-4996-a0b4-9fd27a6c2c6d
[INFO ] 2025-04-28 11:08:58.029 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Start completed.
[INFO ] 2025-04-28 11:08:58.035 [main] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at '**********************************************************************************************************************************kCopy=true;delayLoadingLobs=true;useFmtOnly=false;useBulkCopyForBatchInsert=false;cancelQueryTimeout=-1;sslProtocol=TLS;jaasConfigurationName=SQLJDBCDriver;statementPoolingCacheSize=0;serverPreparedStatementDiscardThreshold=10;enablePrepareOnFirstPreparedStatementCall=false;fips=false;socketTimeout=0;authentication=NotSpecified;authenticationScheme=nativeAuthentication;xopenStates=false;datetimeParameterType=datetime2;sendTimeAsDatetime=true;replication=false;trustStoreType=JKS;trustServerCertificate=true;TransparentNetworkIPResolution=true;iPAddressPreference=IPv4First;serverNameAsACE=false;sendStringParametersAsUnicode=true;selectMethod=direct;responseBuffering=adaptive;queryTimeout=-1;packetSize=8000;multiSubnetFailover=false;loginTimeout=30;lockTimeout=-1;lastUpdateCount=true;prepareMethod=prepexec;encrypt=true;disableStatementPooling=true;databaseName=master;columnEncryptionSetting=Disabled;applicationName=Microsoft JDBC Driver for SQL Server;applicationIntent=readwrite;'
[INFO ] 2025-04-28 11:08:58.129 [main] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
[INFO ] 2025-04-28 11:08:58.151 [main] o.h.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
[INFO ] 2025-04-28 11:08:58.164 [main] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
[INFO ] 2025-04-28 11:08:58.269 [main] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
[WARN ] 2025-04-28 11:08:58.288 [main] o.h.o.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[INFO ] 2025-04-28 11:08:58.441 [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[INFO ] 2025-04-28 11:08:58.443 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
[WARN ] 2025-04-28 11:08:58.523 [main] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[INFO ] 2025-04-28 11:08:58.911 [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
[INFO ] 2025-04-28 11:08:58.942 [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-04-28 11:08:58.949 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/ms-pld/api/v1'
[INFO ] 2025-04-28 11:08:58.962 [main] c.c.MainApplication - Started MainApplication in 2.121 seconds (process running for 2.36)
[INFO ] 2025-04-28 11:09:04.538 [http-nio-8080-exec-1] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO ] 2025-04-28 11:09:04.539 [http-nio-8080-exec-1] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
[INFO ] 2025-04-28 11:09:04.540 [http-nio-8080-exec-1] o.s.w.s.DispatcherServlet - Completed initialization in 1 ms
[INFO ] 2025-04-28 11:10:25.469 [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
[INFO ] 2025-04-28 11:10:25.473 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown initiated...
[INFO ] 2025-04-28 11:10:25.475 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown completed.
