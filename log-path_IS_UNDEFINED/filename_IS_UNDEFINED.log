[INFO ] 2025-06-27 15:46:13.342 [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.2.Final
[INFO ] 2025-06-27 15:46:13.521 [main] c.c.MainApplication - Starting MainApplication using Java 21.0.7 with PID 17016 (/Users/<USER>/Downloads/mspld/ms-pld 2 2/target/classes started by macbookgmn in /Users/<USER>/Downloads/mspld/ms-pld 2 2)
[DEBUG] 2025-06-27 15:46:13.522 [main] c.c.MainApplication - Running with Spring Boot v3.3.11, Spring v6.2.7
[INFO ] 2025-06-27 15:46:13.524 [main] c.c.MainApplication - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-06-27 15:46:16.304 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[INFO ] 2025-06-27 15:46:16.365 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 42 ms. Found 0 JPA repository interfaces.
[INFO ] 2025-06-27 15:46:16.882 [main] o.s.c.c.s.GenericScope - BeanFactory id=827ccb21-f30d-3397-8980-e45a6b33a5f0
[INFO ] 2025-06-27 15:46:18.862 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 8080 (http)
[INFO ] 2025-06-27 15:46:18.902 [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-27 15:46:18.907 [main] o.a.c.c.StandardService - Starting service [Tomcat]
[INFO ] 2025-06-27 15:46:18.912 [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
[INFO ] 2025-06-27 15:46:19.062 [main] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring embedded WebApplicationContext
[INFO ] 2025-06-27 15:46:19.064 [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5428 ms
[INFO ] 2025-06-27 15:46:19.833 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Starting...
[INFO ] 2025-06-27 15:46:20.401 [main] c.z.h.p.HikariPool - SpringBootJPAHikariCP - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
[INFO ] 2025-06-27 15:46:20.404 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Start completed.
[INFO ] 2025-06-27 15:46:20.901 [main] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
[INFO ] 2025-06-27 15:46:21.136 [main] o.h.Version - HHH000412: Hibernate ORM core version 6.5.3.Final
[INFO ] 2025-06-27 15:46:21.204 [main] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
[INFO ] 2025-06-27 15:46:21.841 [main] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
[WARN ] 2025-06-27 15:46:21.958 [main] o.h.o.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[INFO ] 2025-06-27 15:46:22.625 [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[INFO ] 2025-06-27 15:46:22.638 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
[WARN ] 2025-06-27 15:46:23.261 [main] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[INFO ] 2025-06-27 15:46:29.718 [main] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
[INFO ] 2025-06-27 15:46:30.387 [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
[INFO ] 2025-06-27 15:46:30.517 [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
[WARN ] 2025-06-27 15:46:30.540 [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
[INFO ] 2025-06-27 15:46:30.552 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
[INFO ] 2025-06-27 15:46:30.627 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown initiated...
[INFO ] 2025-06-27 15:46:30.652 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown completed.
[INFO ] 2025-06-27 15:46:30.876 [main] o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[ERROR] 2025-06-27 15:46:30.913 [main] o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

[INFO ] 2025-06-27 15:49:27.676 [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.2.Final
[INFO ] 2025-06-27 15:49:27.896 [main] c.c.MainApplication - Starting MainApplication using Java 21.0.7 with PID 17426 (/Users/<USER>/Downloads/mspld/ms-pld 2 2/target/classes started by macbookgmn in /Users/<USER>/Downloads/mspld/ms-pld 2 2)
[DEBUG] 2025-06-27 15:49:27.897 [main] c.c.MainApplication - Running with Spring Boot v3.3.11, Spring v6.2.7
[INFO ] 2025-06-27 15:49:27.902 [main] c.c.MainApplication - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-06-27 15:49:30.213 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[INFO ] 2025-06-27 15:49:30.287 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 57 ms. Found 0 JPA repository interfaces.
[INFO ] 2025-06-27 15:49:30.641 [main] o.s.c.c.s.GenericScope - BeanFactory id=827ccb21-f30d-3397-8980-e45a6b33a5f0
[INFO ] 2025-06-27 15:49:31.937 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 8080 (http)
[INFO ] 2025-06-27 15:49:31.985 [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-27 15:49:31.994 [main] o.a.c.c.StandardService - Starting service [Tomcat]
[INFO ] 2025-06-27 15:49:31.995 [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
[INFO ] 2025-06-27 15:49:32.150 [main] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring embedded WebApplicationContext
[INFO ] 2025-06-27 15:49:32.152 [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4125 ms
[INFO ] 2025-06-27 15:49:32.833 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Starting...
[INFO ] 2025-06-27 15:49:33.341 [main] c.z.h.p.HikariPool - SpringBootJPAHikariCP - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
[INFO ] 2025-06-27 15:49:33.344 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Start completed.
[INFO ] 2025-06-27 15:49:33.585 [main] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
[INFO ] 2025-06-27 15:49:33.679 [main] o.h.Version - HHH000412: Hibernate ORM core version 6.5.3.Final
[INFO ] 2025-06-27 15:49:33.747 [main] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
[INFO ] 2025-06-27 15:49:34.247 [main] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
[WARN ] 2025-06-27 15:49:34.358 [main] o.h.o.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[INFO ] 2025-06-27 15:49:35.042 [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[INFO ] 2025-06-27 15:49:35.056 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
[WARN ] 2025-06-27 15:49:35.790 [main] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[INFO ] 2025-06-27 15:49:38.169 [main] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
[INFO ] 2025-06-27 15:49:38.395 [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
[INFO ] 2025-06-27 15:49:38.715 [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-27 15:49:38.813 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/ms-pld/api/v1'
[INFO ] 2025-06-27 15:49:38.929 [main] c.c.MainApplication - Started MainApplication in 12.814 seconds (process running for 14.256)
[INFO ] 2025-06-27 15:50:26.534 [http-nio-8080-exec-1] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO ] 2025-06-27 15:50:26.535 [http-nio-8080-exec-1] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
[INFO ] 2025-06-27 15:50:26.539 [http-nio-8080-exec-1] o.s.w.s.DispatcherServlet - Completed initialization in 3 ms
[INFO ] 2025-06-27 15:50:26.679 [http-nio-8080-exec-1] c.c.c.CatalogosController - [CATALOGOS] Iniciando obtención de todos los catálogos
[WARN ] 2025-06-27 15:50:26.910 [http-nio-8080-exec-1] o.h.e.j.s.SqlExceptionHelper - SQL Error: 42102, SQLState: 42S02
[ERROR] 2025-06-27 15:50:26.911 [http-nio-8080-exec-1] o.h.e.j.s.SqlExceptionHelper - Tabla "CATALOGOSFECHAACTUALIZACION" no encontrada
Table "CATALOGOSFECHAACTUALIZACION" not found; SQL statement:
SELECT id, catalogo, fecha, isActive from CatalogosFechaActualizacion [42102-224]
[ERROR] 2025-06-27 15:50:26.933 [http-nio-8080-exec-1] c.c.c.CatalogosController - [CATALOGOS] Error al obtener catálogos: 406 NOT_ACCEPTABLE "Error al obtener catalogos"
org.springframework.web.server.ResponseStatusException: 406 NOT_ACCEPTABLE "Error al obtener catalogos"
	at com.coppel.repositories.CatalogoRepository.obtenerCats(CatalogoRepository.java:46)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.coppel.repositories.CatalogoRepository$$SpringCGLIB$$0.obtenerCats(<generated>)
	at com.coppel.services.impl.CatalogoServiceImpl.obtenerCats(CatalogoServiceImpl.java:87)
	at com.coppel.controllers.CatalogosController.obtenerCats(CatalogosController.java:36)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.coppel.filters.SessionFilter.doFilter(SessionFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Tabla "CATALOGOSFECHAACTUALIZACION" no encontrada
Table "CATALOGOSFECHAACTUALIZACION" not found; SQL statement:
SELECT id, catalogo, fecha, isActive from CatalogosFechaActualizacion [42102-224]] [SELECT id, catalogo, fecha, isActive from CatalogosFechaActualizacion]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:194)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:155)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.lambda$list$0(JdbcSelectExecutor.java:85)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:231)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:167)
	at org.hibernate.sql.results.jdbc.internal.AbstractResultSetAccess.getMetaData(AbstractResultSetAccess.java:36)
	at org.hibernate.sql.results.jdbc.internal.AbstractResultSetAccess.getColumnCount(AbstractResultSetAccess.java:52)
	at org.hibernate.query.results.ResultSetMappingImpl.resolve(ResultSetMappingImpl.java:193)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:327)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:115)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:83)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:76)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:65)
	at org.hibernate.query.sql.internal.NativeSelectQueryPlanImpl.performList(NativeSelectQueryPlanImpl.java:138)
	at org.hibernate.query.sql.internal.NativeQueryImpl.doList(NativeQueryImpl.java:628)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:423)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at com.coppel.repositories.CatalogoRepository.obtenerCats(CatalogoRepository.java:43)
	... 72 common frames omitted
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Tabla "CATALOGOSFECHAACTUALIZACION" no encontrada
Table "CATALOGOSFECHAACTUALIZACION" not found; SQL statement:
SELECT id, catalogo, fecha, isActive from CatalogosFechaActualizacion [42102-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8064)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8035)
	at org.h2.command.Parser.readTableOrView(Parser.java:8024)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1788)
	at org.h2.command.Parser.readTableReference(Parser.java:2268)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2718)
	at org.h2.command.Parser.parseSelect(Parser.java:2824)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2708)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2564)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2543)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2536)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2529)
	at org.h2.command.Parser.parseQuery(Parser.java:2498)
	at org.h2.command.Parser.parsePrepared(Parser.java:627)
	at org.h2.command.Parser.parse(Parser.java:592)
	at org.h2.command.Parser.parse(Parser.java:564)
	at org.h2.command.Parser.prepareCommand(Parser.java:483)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:639)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:559)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1166)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:316)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:328)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$5.doPrepare(StatementPreparerImpl.java:153)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:183)
	... 89 common frames omitted
[WARN ] 2025-06-27 15:50:26.964 [http-nio-8080-exec-1] c.c.e.AppExceptionHandler - [time: 2025-06-27T15:50:26.964176] [ip: ************] [user: sa] -- [ GET 500 ]
org.springframework.web.server.ResponseStatusException: 406 NOT_ACCEPTABLE "Error al obtener catalogos"
	at com.coppel.repositories.CatalogoRepository.obtenerCats(CatalogoRepository.java:46)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.coppel.repositories.CatalogoRepository$$SpringCGLIB$$0.obtenerCats(<generated>)
	at com.coppel.services.impl.CatalogoServiceImpl.obtenerCats(CatalogoServiceImpl.java:87)
	at com.coppel.controllers.CatalogosController.obtenerCats(CatalogosController.java:36)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.coppel.filters.SessionFilter.doFilter(SessionFilter.java:63)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Tabla "CATALOGOSFECHAACTUALIZACION" no encontrada
Table "CATALOGOSFECHAACTUALIZACION" not found; SQL statement:
SELECT id, catalogo, fecha, isActive from CatalogosFechaActualizacion [42102-224]] [SELECT id, catalogo, fecha, isActive from CatalogosFechaActualizacion]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:194)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:155)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.lambda$list$0(JdbcSelectExecutor.java:85)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:231)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:167)
	at org.hibernate.sql.results.jdbc.internal.AbstractResultSetAccess.getMetaData(AbstractResultSetAccess.java:36)
	at org.hibernate.sql.results.jdbc.internal.AbstractResultSetAccess.getColumnCount(AbstractResultSetAccess.java:52)
	at org.hibernate.query.results.ResultSetMappingImpl.resolve(ResultSetMappingImpl.java:193)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:327)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:115)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:83)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:76)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:65)
	at org.hibernate.query.sql.internal.NativeSelectQueryPlanImpl.performList(NativeSelectQueryPlanImpl.java:138)
	at org.hibernate.query.sql.internal.NativeQueryImpl.doList(NativeQueryImpl.java:628)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:423)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at com.coppel.repositories.CatalogoRepository.obtenerCats(CatalogoRepository.java:43)
	... 72 common frames omitted
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Tabla "CATALOGOSFECHAACTUALIZACION" no encontrada
Table "CATALOGOSFECHAACTUALIZACION" not found; SQL statement:
SELECT id, catalogo, fecha, isActive from CatalogosFechaActualizacion [42102-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8064)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8035)
	at org.h2.command.Parser.readTableOrView(Parser.java:8024)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1788)
	at org.h2.command.Parser.readTableReference(Parser.java:2268)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2718)
	at org.h2.command.Parser.parseSelect(Parser.java:2824)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2708)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2564)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2543)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2536)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2529)
	at org.h2.command.Parser.parseQuery(Parser.java:2498)
	at org.h2.command.Parser.parsePrepared(Parser.java:627)
	at org.h2.command.Parser.parse(Parser.java:592)
	at org.h2.command.Parser.parse(Parser.java:564)
	at org.h2.command.Parser.prepareCommand(Parser.java:483)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:639)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:559)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1166)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:316)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:328)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$5.doPrepare(StatementPreparerImpl.java:153)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:183)
	... 89 common frames omitted
[INFO ] 2025-06-27 15:58:00.516 [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
[INFO ] 2025-06-27 15:58:00.520 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown initiated...
[INFO ] 2025-06-27 15:58:00.525 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown completed.
[INFO ] 2025-06-27 15:58:18.697 [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.2.Final
[INFO ] 2025-06-27 15:58:18.838 [main] c.c.MainApplication - Starting MainApplication using Java 21.0.7 with PID 18141 (/Users/<USER>/Downloads/mspld/ms-pld 2 2/target/classes started by macbookgmn in /Users/<USER>/Downloads/mspld/ms-pld 2 2)
[DEBUG] 2025-06-27 15:58:18.838 [main] c.c.MainApplication - Running with Spring Boot v3.3.11, Spring v6.2.7
[INFO ] 2025-06-27 15:58:18.840 [main] c.c.MainApplication - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-06-27 15:58:20.715 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[INFO ] 2025-06-27 15:58:20.784 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 JPA repository interfaces.
[INFO ] 2025-06-27 15:58:21.226 [main] o.s.c.c.s.GenericScope - BeanFactory id=827ccb21-f30d-3397-8980-e45a6b33a5f0
[INFO ] 2025-06-27 15:58:22.443 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 8080 (http)
[INFO ] 2025-06-27 15:58:22.471 [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-27 15:58:22.477 [main] o.a.c.c.StandardService - Starting service [Tomcat]
[INFO ] 2025-06-27 15:58:22.477 [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
[INFO ] 2025-06-27 15:58:22.627 [main] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring embedded WebApplicationContext
[INFO ] 2025-06-27 15:58:22.629 [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3699 ms
[INFO ] 2025-06-27 15:58:23.465 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Starting...
[INFO ] 2025-06-27 15:58:23.859 [main] c.z.h.p.HikariPool - SpringBootJPAHikariCP - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
[INFO ] 2025-06-27 15:58:23.863 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Start completed.
[INFO ] 2025-06-27 15:58:24.045 [main] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
[INFO ] 2025-06-27 15:58:24.115 [main] o.h.Version - HHH000412: Hibernate ORM core version 6.5.3.Final
[INFO ] 2025-06-27 15:58:24.164 [main] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
[INFO ] 2025-06-27 15:58:24.561 [main] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
[WARN ] 2025-06-27 15:58:24.652 [main] o.h.o.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[INFO ] 2025-06-27 15:58:25.178 [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[INFO ] 2025-06-27 15:58:25.187 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
[WARN ] 2025-06-27 15:58:25.721 [main] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[INFO ] 2025-06-27 15:58:27.703 [main] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
[INFO ] 2025-06-27 15:58:27.945 [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
[INFO ] 2025-06-27 15:58:28.108 [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-27 15:58:28.189 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/ms-pld/api/v1'
[INFO ] 2025-06-27 15:58:28.255 [main] c.c.MainApplication - Started MainApplication in 10.437 seconds (process running for 11.341)
[INFO ] 2025-06-27 15:58:36.085 [http-nio-8080-exec-1] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[INFO ] 2025-06-27 15:58:36.086 [http-nio-8080-exec-1] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
[INFO ] 2025-06-27 15:58:36.089 [http-nio-8080-exec-1] o.s.w.s.DispatcherServlet - Completed initialization in 3 ms
[INFO ] 2025-06-27 16:11:52.489 [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
[INFO ] 2025-06-27 16:11:52.495 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown initiated...
[INFO ] 2025-06-27 16:11:52.503 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown completed.
