[INFO ] 2025-06-30 14:21:28.253 [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.2.Final
[INFO ] 2025-06-30 14:21:28.392 [main] c.c.MainApplication - Starting MainApplication using Java 17.0.15 with PID 22751 (/Users/<USER>/Downloads/mspld/ms-pld 2 2/target/classes started by macbookgmn in /Users/<USER>/Downloads/mspld/ms-pld 2 2)
[DEBUG] 2025-06-30 14:21:28.393 [main] c.c.MainApplication - Running with Spring Boot v3.3.11, Spring v6.2.7
[INFO ] 2025-06-30 14:21:28.395 [main] c.c.MainApplication - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-06-30 14:21:30.615 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[INFO ] 2025-06-30 14:21:30.661 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.
[INFO ] 2025-06-30 14:21:30.959 [main] o.s.c.c.s.GenericScope - BeanFactory id=827ccb21-f30d-3397-8980-e45a6b33a5f0
[INFO ] 2025-06-30 14:21:32.111 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 8080 (http)
[INFO ] 2025-06-30 14:21:32.138 [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-30 14:21:32.143 [main] o.a.c.c.StandardService - Starting service [Tomcat]
[INFO ] 2025-06-30 14:21:32.144 [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
[INFO ] 2025-06-30 14:21:32.299 [main] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring embedded WebApplicationContext
[INFO ] 2025-06-30 14:21:32.301 [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3815 ms
[DEBUG] 2025-06-30 14:21:32.870 [main] o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
[INFO ] 2025-06-30 14:21:32.985 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Starting...
[INFO ] 2025-06-30 14:21:33.487 [main] c.z.h.p.HikariPool - SpringBootJPAHikariCP - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
[INFO ] 2025-06-30 14:21:33.490 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Start completed.
[INFO ] 2025-06-30 14:21:33.950 [main] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
[INFO ] 2025-06-30 14:21:34.164 [main] o.h.Version - HHH000412: Hibernate ORM core version 6.5.3.Final
[INFO ] 2025-06-30 14:21:34.224 [main] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
[INFO ] 2025-06-30 14:21:34.800 [main] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
[WARN ] 2025-06-30 14:21:34.864 [main] o.h.o.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[INFO ] 2025-06-30 14:21:35.383 [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[INFO ] 2025-06-30 14:21:35.392 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
[WARN ] 2025-06-30 14:21:35.826 [main] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[DEBUG] 2025-06-30 14:21:36.381 [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping - 19 mappings in 'requestMappingHandlerMapping'
[DEBUG] 2025-06-30 14:21:36.614 [main] o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
[DEBUG] 2025-06-30 14:21:36.719 [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[DEBUG] 2025-06-30 14:21:36.830 [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
[INFO ] 2025-06-30 14:21:37.369 [main] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
[INFO ] 2025-06-30 14:21:37.493 [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
[INFO ] 2025-06-30 14:21:37.590 [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-30 14:21:37.778 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/ms-pld/api/v1'
[INFO ] 2025-06-30 14:21:37.849 [main] c.c.MainApplication - Started MainApplication in 10.228 seconds (process running for 11.09)
[INFO ] 2025-06-30 14:21:47.953 [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
[INFO ] 2025-06-30 14:21:47.956 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown initiated...
[INFO ] 2025-06-30 14:21:47.963 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown completed.
[INFO ] 2025-06-30 14:22:07.899 [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 8.0.2.Final
[INFO ] 2025-06-30 14:22:08.017 [main] c.c.MainApplication - Starting MainApplication using Java 17.0.15 with PID 22820 (/Users/<USER>/Downloads/mspld/ms-pld 2 2/target/classes started by macbookgmn in /Users/<USER>/Downloads/mspld/ms-pld 2 2)
[DEBUG] 2025-06-30 14:22:08.020 [main] c.c.MainApplication - Running with Spring Boot v3.3.11, Spring v6.2.7
[INFO ] 2025-06-30 14:22:08.022 [main] c.c.MainApplication - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-06-30 14:22:09.669 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[INFO ] 2025-06-30 14:22:09.712 [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 JPA repository interfaces.
[INFO ] 2025-06-30 14:22:09.993 [main] o.s.c.c.s.GenericScope - BeanFactory id=827ccb21-f30d-3397-8980-e45a6b33a5f0
[INFO ] 2025-06-30 14:22:10.993 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port 8080 (http)
[INFO ] 2025-06-30 14:22:11.029 [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-30 14:22:11.035 [main] o.a.c.c.StandardService - Starting service [Tomcat]
[INFO ] 2025-06-30 14:22:11.036 [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
[INFO ] 2025-06-30 14:22:11.246 [main] o.a.c.c.C.[.[.[/ms-pld/api/v1] - Initializing Spring embedded WebApplicationContext
[INFO ] 2025-06-30 14:22:11.249 [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3130 ms
[DEBUG] 2025-06-30 14:22:11.577 [main] o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
[INFO ] 2025-06-30 14:22:11.671 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Starting...
[INFO ] 2025-06-30 14:22:12.212 [main] c.z.h.p.HikariPool - SpringBootJPAHikariCP - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
[INFO ] 2025-06-30 14:22:12.215 [main] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Start completed.
[INFO ] 2025-06-30 14:22:12.464 [main] o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
[INFO ] 2025-06-30 14:22:12.551 [main] o.h.Version - HHH000412: Hibernate ORM core version 6.5.3.Final
[INFO ] 2025-06-30 14:22:12.609 [main] o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
[INFO ] 2025-06-30 14:22:13.928 [main] o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
[WARN ] 2025-06-30 14:22:14.098 [main] o.h.o.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[INFO ] 2025-06-30 14:22:14.868 [main] o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[INFO ] 2025-06-30 14:22:14.885 [main] o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
[WARN ] 2025-06-30 14:22:15.962 [main] o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[DEBUG] 2025-06-30 14:22:17.301 [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping - 19 mappings in 'requestMappingHandlerMapping'
[DEBUG] 2025-06-30 14:22:17.634 [main] o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
[DEBUG] 2025-06-30 14:22:17.741 [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[DEBUG] 2025-06-30 14:22:17.856 [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
[INFO ] 2025-06-30 14:22:18.764 [main] o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:testdb'
[INFO ] 2025-06-30 14:22:18.930 [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
[INFO ] 2025-06-30 14:22:19.048 [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
[INFO ] 2025-06-30 14:22:19.083 [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/ms-pld/api/v1'
[INFO ] 2025-06-30 14:22:19.119 [main] c.c.MainApplication - Started MainApplication in 11.856 seconds (process running for 12.729)
[INFO ] 2025-06-30 14:23:33.862 [SpringApplicationShutdownHook] o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
[INFO ] 2025-06-30 14:23:33.874 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown initiated...
[INFO ] 2025-06-30 14:23:33.881 [SpringApplicationShutdownHook] c.z.h.HikariDataSource - SpringBootJPAHikariCP - Shutdown completed.
